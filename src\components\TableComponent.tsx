import React from "react";
import { Table, Pagination } from "antd";
import type { ColumnsType } from "antd/es/table";
import PaginationComponent from "./PaginationComponent";

interface DataType {
  [key: string]: string | number;
}

interface AntDTableProps<T> {
  columns: ColumnsType<T>;
  data: T[];
  title: string;
  loading?: boolean;
  scrollX?: number | string;
  pageSize?: number; // Add optional page size
}

function AntDTable<T extends DataType>({
  columns,
  data,
  title,
  scrollX,
  pageSize = 10,
  loading = false,
}: AntDTableProps<T>) {
  const [currentPage, setCurrentPage] = React.useState(1);

  // Calculate total pages based on data length and page size
  const totalPages = Math.ceil(data.length / pageSize);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Slice data based on current page
  const paginatedData = data.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <div>
      <Table
        columns={columns}
        dataSource={paginatedData}
        className="border border-gray-100 rounded-md"
        pagination={false} // Disable default pagination
        scroll={{ x: scrollX }}
        loading={loading}
        rowClassName={() => "table-row"} // Optionally add a class for row styling
        components={{
          header: {
            cell: (props: any) => (
              <th
                {...props}
                style={{
                  padding: "4px",
                  paddingLeft: "16px",
                  textAlign: "left",
                }}
              />
            ),
          },
        }}
      />
      <div className="">
        <PaginationComponent
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={4}
          onPageChange={handlePageChange}
          onPageSizeChange={setCurrentPage}
        />
      </div>
    </div>
  );
}

export default AntDTable;
