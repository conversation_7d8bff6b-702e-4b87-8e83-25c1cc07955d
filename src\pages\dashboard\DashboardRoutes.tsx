import { Routes, Route } from "react-router-dom";
import Home from "./home";
import Bookings from "./bookings";
import Targets from "./portfolio";
import Listings from "./listings";
import Performance from "./performance";
import ChatBox from "./chatbox/ChatPage";
import ArticleEditor from "./listings/Content/createArticle";
import ArticlePage from "./listings/Content/articleViewer";
import Calendar from "./Calendar";

const DashboardRoutes = () => {
  return (
    <Routes>
      <Route path="/home" element={<Home />} />
      <Route path="/bookings" element={<Bookings />} />
      <Route path="/reports/targets" element={<Targets />} />
      <Route path="/reports/channelPerformance" element={<Targets />} />
      <Route path="/chatbox" element={<ChatBox />} />
      <Route path="/listings" element={<Listings />} />
      <Route path="/listings/articles/new" element={<ArticleEditor />} />
      <Route path="/listings/articles/edit/:articleId" element={<ArticleEditor />} />
      <Route path="/listings/articles/view/:articleId" element={<ArticlePage />} />
      <Route path="/performance" element={<Performance />} />
      <Route path="/calendar" element={<Calendar />} />
      <Route path="*" element={<Home />} /> {/* Fallback or a 404 component */}
    </Routes>
  );
};

export default DashboardRoutes;
