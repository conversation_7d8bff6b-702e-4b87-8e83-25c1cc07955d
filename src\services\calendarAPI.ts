import apiClient from "./apiClient";

// Types for Calendar API
export interface CalendarProperty {
  id: string | number;
  listingid: string;
  name: string;
  internal_listing_name: string;
}

export interface PropertyAvailability {
  date: string;
  price: number;
  min_night_stay: number;
  max_night_stay: number;
  status: 'available' | 'blocked' | 'booked';
  reservation_id?: string;
}

export interface PropertyReservation {
  reservation_id: string;
  pms_table_reservation_id: string;
  guest_name: string;
  arrival_date: string;
  departure_date: string;
  channel: string;
  total_amount: number;
  status: string;
}

export interface CalendarData {
  success: boolean;
  data: {
    properties: Array<{
      id: string | number;
      listingid: string;
      name: string;
      internal_listing_name: string;
    }>;
    calendar_data: {
      [propertyId: string]: {
        availability: {
          [date: string]: {
            price: number;
            min_nights: number;
            max_nights: number;
            status: string;
            notes?: string;
          };
        };
        reservations: {
          [date: string]: {
            guest_name: string;
            channel: string;
            total_price: number;
            status: string;
            arrival_date: string;
            departure_date: string;
          };
        };
      };
    };
    date_range: {
      start_date: string;
      end_date: string;
    };
    pagination?: {
      current_page: number;
      page_size: number;
      total_properties: number;
      total_pages: number;
      has_next: boolean;
      has_previous: boolean;
    };
  };
}

export interface AvailabilitySummary {
  available_count: number;
  blocked_count: number;
  booked_count: number;
  total_properties: number;
  date_range: {
    start_date: string;
    end_date: string;
  };
}

export interface BulkUpdateRequest {
  updates: Array<{
    property_id: string | number;
    date: string;
    price?: number;
    min_night_stay?: number;
    max_night_stay?: number;
    status?: 'available' | 'blocked' | 'booked';
  }>;
}

export interface BulkUpdateResponse {
  success: boolean;
  updated_count: number;
  errors: Array<{
    property_id: string | number;
    date: string;
    error: string;
  }>;
}

// Calendar API Parameters
export interface CalendarParams {
  start_date: string;
  end_date: string;
  user_group_id?: string | number;
  property_ids?: string[];
  page?: number;
  page_size?: number;
  search?: string;
}

export interface SummaryParams {
  start_date: string;
  end_date: string;
  user_group_id?: string | number;
  property_ids?: string[];
}

/**
 * Fetch calendar data with availability and reservations
 * Optimized for calendar view with pagination
 */
export const fetchCalendarData = async (params: CalendarParams): Promise<CalendarData> => {
  const requestBody: Record<string, any> = {};
  
  // Add all parameters to request body
  if (params.start_date) {
    requestBody.start_date = params.start_date;
  }
  
  if (params.end_date) {
    requestBody.end_date = params.end_date;
  }
  
  if (params.user_group_id) {
    requestBody.user_group_id = params.user_group_id;
  }
  
  if (params.property_ids && params.property_ids.length > 0) {
    requestBody.property_ids = params.property_ids;
  }
  
  if (params.page) {
    requestBody.page = params.page;
  }
  
  if (params.page_size) {
    requestBody.page_size = params.page_size;
  }
  
  if (params.search) {
    requestBody.search = params.search;
  }

  const response = await apiClient.post('/api/availability/calendar/', requestBody);
  return response.data;
};

export const fetchAvailabilitySummary = async (params: SummaryParams): Promise<AvailabilitySummary> => {
  const queryParams = new URLSearchParams();
  
  queryParams.append('start_date', params.start_date);
  queryParams.append('end_date', params.end_date);
  
  if (params.user_group_id) {
    queryParams.append('user_group_id', params.user_group_id.toString());
  }
  
  if (params.property_ids && params.property_ids.length > 0) {
    params.property_ids.forEach(id => queryParams.append('property_ids', id));
  }

  const response = await apiClient.get(`/api/availability/summary/?${queryParams.toString()}/`);
  return response.data;
};

export const bulkUpdateAvailability = async (data: BulkUpdateRequest): Promise<BulkUpdateResponse> => {
  const response = await apiClient.post('/api/availability/bulk-update/', data);
  return response.data;
};

/**
 * Fetch properties list for calendar (lightweight)
 * Used for property selection and filtering
 */
export const fetchCalendarProperties = async (params: {
  search?: string;
  user_group_id?: string | number;
  page?: number;
  page_size?: number;
}): Promise<{
  results: CalendarProperty[];
  count: number;
  next: string | null;
  previous: string | null;
}> => {
  const queryParams = new URLSearchParams();
  
  if (params.search) {
    queryParams.append('search', params.search);
  }
  
  if (params.user_group_id) {
    queryParams.append('user_group_id', params.user_group_id.toString());
  }
  
  if (params.page) {
    queryParams.append('page', params.page.toString());
  }
  
  if (params.page_size) {
    queryParams.append('page_size', params.page_size.toString());
  }

  const response = await apiClient.get(`/api/listings/summary/?${queryParams.toString()}/`);
  return response.data;
};

// Utility functions for date handling
export const formatDateForAPI = (date: Date): string => {
  return date.toISOString().split('T')[0]; // YYYY-MM-DD format
};

export const getDateRange = (startDate: Date, endDate: Date): string[] => {
  const dates: string[] = [];
  const currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    dates.push(formatDateForAPI(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return dates;
};

// Cache management utilities
export const getCacheKey = (prefix: string, params: any): string => {
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result: Record<string, any>, key: string) => {
      result[key] = params[key];
      return result;
    }, {});
  
  return `${prefix}_${JSON.stringify(sortedParams)}`;
};