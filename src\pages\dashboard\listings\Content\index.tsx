import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Checkbox,
  Input,
  Table,
  Button,
  Dropdown,
  Menu,
  Space,
  Modal,
} from "antd";
import { MoreOutlined, PlusOutlined, FolderAddFilled } from "@ant-design/icons";
import { Card } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { createFolder, fetchArticles } from "../../../../services/targetAPI";

const { Search } = Input;

const ContentPage = ({
  selectedContentTab,
  loadFolders,
  folders,
}: {
  selectedContentTab: string | number | undefined;
  loadFolders: () => Promise<void>;
  folders: any[];
}) => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState("");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [folderName, setFolderName] = useState("");
  
  // Add ref to track ongoing requests and prevent race conditions
  const loadingRef = useRef(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Define the Article interface
  interface Article {
    id: string;
    title: string;
    slug: string;
    folder: number | null;
    folder_name?: string;
    content_type: string;
    content_preview: string;
    created_by_name: string;
    formatted_created_at: string;
    formatted_updated_at: string;
  }
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  const getFolderNameById = (folderId: string | number | undefined) => {
    if (!folderId || !folders) return null;
    const folder = folders.find(f => f.id === folderId);
    return folder ? folder.name : null;
  };

  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (str: string) => {
    if (!str) return "";
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  };

  // Helper function to format content type
  const formatContentType = (type: string) => {
    switch (type) {
      case "internal_article":
        return "Internal Article";
      case "public_article":
        return "Public Article";
      default:
        return type;
    }
  };

  // Updated columns to match API response structure
  const columns = [
    { title: "Title", dataIndex: "title", key: "title" },
    {
      title: "Folder",
      dataIndex: "folder_name",
      key: "folder_name",
      render: (folder_name: string) => folder_name || "No Folder",
    },
    {
      title: "Content type",
      dataIndex: "content_type",
      key: "content_type",
      render: (type: string) => formatContentType(type),
    },
    {
      title: "Last updated at",
      dataIndex: "formatted_updated_at",
      key: "formatted_updated_at",
      render: (date: string) => {
        return date;
      },
    },
    {
      title: "Last updated by",
      dataIndex: "updated_by_name",
      key: "updated_by_name",
      render: (name: string) => capitalizeFirstLetter(name),
    },
    {
      title: "Created by",
      dataIndex: "created_by_name",
      key: "created_by_name",
      render: (name: string) => capitalizeFirstLetter(name),
    },
  ];

  // Function to load articles with race condition prevention
  const loadArticles = useCallback(
    async (page = 1, search = "", folder?: string | number, pageSize = 10, isSearching = false) => {
      // Prevent multiple simultaneous requests
      if (loadingRef.current && !isSearching) {
        return;
      }
      
      loadingRef.current = true;
      setLoading(true);
      
      try {
        const params: any = {
          page,
          page_size: pageSize,
        };

        // Add search if provided
        if (search) {
          params.search = search;
        }

        // Add folder filter - if selectedContentTab is defined, filter by that folder
        if (folder !== undefined) {
          params.folder = folder;
        }

        const response = await fetchArticles(params);

        if (response.success) {
          setArticles(response.data);
          setPagination((prev) => ({
            ...prev,
            current: page,
            pageSize: pageSize,
            total: response.pagination?.count || 0,
          }));
        }
      } catch (error) {
        console.error("Failed to fetch articles:", error);
      } finally {
        setLoading(false);
        loadingRef.current = false;
      }
    },
    []
  );

  // Load articles when component mounts or selectedContentTab changes
  useEffect(() => {
    // Clear any pending search timeout when folder changes
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }
    
    loadArticles(1, searchText, selectedContentTab, pagination.pageSize);
  }, [selectedContentTab, loadArticles]);

  // Handle search with debounce - SEPARATE from folder changes
  useEffect(() => {
    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Don't debounce if search is empty (immediate load)
    if (searchText === "") {
      loadArticles(1, searchText, selectedContentTab, pagination.pageSize, true);
      return;
    }
    
    // Debounce search
    searchTimeoutRef.current = setTimeout(() => {
      loadArticles(1, searchText, selectedContentTab, pagination.pageSize, true);
    }, 500);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchText, loadArticles]); // Remove selectedContentTab from deps to avoid double calls

  // Handle pagination change
  const handleTableChange = (paginationInfo: any) => {
    const { current, pageSize } = paginationInfo;
    loadArticles(current, searchText, selectedContentTab, pageSize);
  };

  const handleCreateFolder = async () => {
    try {
      const response = await createFolder({ name: folderName });
      setIsModalVisible(false);
      setFolderName("");
      loadFolders();
      // Refresh articles after creating folder
      loadArticles(pagination.current, searchText, selectedContentTab, pagination.pageSize);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex flex-col h-full p-6 bg-gray-50 space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">
          Content
          {selectedContentTab && (
            <span className="text-lg font-normal text-gray-600 ml-2">
              - {getFolderNameById(selectedContentTab)}
            </span>
          )}
        </h2>
        <Space>
          <Button
            icon={<FolderAddFilled />}
            className="bg-gray-700"
            onClick={() => setIsModalVisible(true)}
          >
            New folder
          </Button>
          <Button
            icon={<PlusOutlined />}
            type="primary"
            className="!bg-black !text-white"
            onClick={() => navigate("/dashboard/listings/articles/new")}
          >
            New content
          </Button>
        </Space>
      </div>

      {/* Search Input */}
      <Search
        placeholder="Search articles..."
        onChange={(e) => setSearchText(e.target.value)}
        value={searchText}
        allowClear
        className="w-60"
      />

      {/* Scrollable Table Section */}
      <div className="flex-1 overflow-y-auto">
        <Card variant="outlined">
          <Table
            dataSource={articles}
            columns={columns}
            loading={loading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} articles`,
              pageSizeOptions: ['10', '20', '50', '100'],
            }}
            onChange={handleTableChange}
            rowClassName={() => "hover:bg-gray-100 cursor-pointer"}
            onRow={(record: Article) => {
              return {
                onClick: () => {
                  const articleId = record.id;
                  if (articleId) {
                    navigate(`/dashboard/listings/articles/view/${articleId}`);
                  }
                },
              };
            }}
            rowKey="id"
          />
        </Card>
      </div>

      <Modal
        title="Create New Folder"
        open={isModalVisible}
        onOk={handleCreateFolder}
        onCancel={() => setIsModalVisible(false)}
        okText="Create"
      >
        <Input
          placeholder="Enter folder name"
          value={folderName}
          onChange={(e) => setFolderName(e.target.value)}
        />
      </Modal>
    </div>
  );
};

export default ContentPage;