import React from "react";
import { Listing, Booking, Rate, Block } from "./types";
import CalendarCell from "./CalendarCell";

interface ListingRowProps {
  listing: Listing;
  visibleDates: Date[];
  bookings: Booking[];
  rates: { [date: string]: Rate };
  blockedNights: string[];
  blockedNotes?: { [date: string]: string };
  blockSpans?: Array<{
    start: string;
    end: string;
    dates: string[];
    notes?: string;
  }>;
  isLast?: boolean;
  listingColumnWidth: number;
  onResizeStart: (e: React.MouseEvent) => void;
}

// Enhanced interface for individual date analysis
interface IndividualDateAnalysis {
  date: Date;
  dateKey: string;
  index: number;
  allBookings: Booking[];
  blocks: Block[];
  rate?: Rate;
  // NEW: Add specific booking analysis for this date
  hasCheckIn: boolean;
  hasCheckOut: boolean;
  checkInBooking?: Booking;
  checkOutBooking?: Booking;
}

const ListingRow: React.FC<ListingRowProps> = ({
  listing,
  visibleDates,
  bookings,
  rates,
  blockedNights,
  blockedNotes = {},
  blockSpans = [],
  isLast = false,
  listingColumnWidth,
  onResizeStart,
}) => {
  const isPreviousDateIncluded = visibleDates.length === 8; // 1 previous + 7 visible
  const previousDate = isPreviousDateIncluded ? visibleDates[0] : null;
  const actualVisibleDates = isPreviousDateIncluded
    ? visibleDates.slice(1)
    : visibleDates;

  const formatDateKey = (date: Date): string => {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
  };

  const normalizeDateFormat = (
    dateStr: string | undefined
  ): string | undefined => {
    if (!dateStr) return undefined;

    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      return dateStr;
    }

    if (/^\d{2}-\d{2}-\d{4}$/.test(dateStr)) {
      const [day, month, year] = dateStr.split("-");
      return `${year}-${month}-${day}`;
    }

    try {
      const parsedDate = new Date(dateStr);
      if (!isNaN(parsedDate.getTime())) {
        return formatDateKey(parsedDate);
      }
    } catch (error) {
      console.warn(`Failed to parse date: ${dateStr}`);
    }

    return dateStr;
  };

  const getBookingForDate = (date: Date): Booking | undefined => {
    const dateKey = formatDateKey(date);

    const allBookingsForDate = bookings.filter((booking) => {
      if (booking.listingUID !== listing.uid) return false;

      if (booking.arrival_date && booking.departure_date) {
        const normalizedArrivalDate = normalizeDateFormat(booking.arrival_date);
        const normalizedDepartureDate = normalizeDateFormat(
          booking.departure_date
        );

        if (!normalizedArrivalDate || !normalizedDepartureDate) {
          return false;
        }

        // FIXED: Only mark dates inside [arrival, departure) as occupied
        // This excludes the checkout day from being grouped with the booking
        const isInRange =
          dateKey >= normalizedArrivalDate && dateKey < normalizedDepartureDate;

        return isInRange;
      }

      return booking.date === dateKey;
    });

    return allBookingsForDate[0];
  };

  const getAllBookingsForSlantingAnalysis = (_currentDate: Date): Booking[] => {
    const allListingBookings = bookings.filter(
      (booking) => booking.listingUID === listing.uid
    );
    return allListingBookings;
  };

  const getRateForDate = (date: Date): Rate | undefined => {
    
    const dateKey = formatDateKey(date);
    const day = date.getDate();
    const isBlocked = isDateBlocked(date);
    const rateData = rates[dateKey];
    return rates[dateKey];
  };

  // NEW: Get previous date's rate for availability extension
  const getPreviousDateRate = (currentDate: Date): Rate => {
    const previousDay = new Date(currentDate);
    previousDay.setDate(previousDay.getDate() - 1);
    const previousDateKey = formatDateKey(previousDay);

    const rate = rates[previousDateKey];

    if (rate) return rate;

    return {
      listingUID: listing.uid,
      date: previousDateKey,
      rate: 0,
      minNights: 0,
    } as Rate;
  };

  const hasPreviousDateAvailability = (currentDate: Date): boolean => {
    const previousDay = new Date(currentDate);
    previousDay.setDate(previousDay.getDate() - 1);
    const previousDateKey = formatDateKey(previousDay);

    const hadBooking = getBookingForDate(previousDay);
    if (hadBooking) return false;

    const wasBlocked = blockedNights.includes(previousDateKey);
    if (wasBlocked) return false;

    return true;
  };

  const isDateBlocked = (date: Date): boolean => {
    const dateKey = formatDateKey(date);
    return blockedNights.includes(dateKey);
  };

  const getNotesForDate = (date: Date): string | undefined => {
    const dateKey = formatDateKey(date);
    return blockedNotes[dateKey];
  };

  const getBlocksForDate = (date: Date): Block[] => {
    const dateKey = formatDateKey(date);

    if (isDateBlocked(date)) {
      // Find the actual block span that contains this date
      const containingSpan = blockSpans.find((span) =>
        span.dates.includes(dateKey)
      );

      if (containingSpan) {
        return [
          {
            listingUID: listing.uid,
            start_date: containingSpan.start,
            end_date: containingSpan.end,
            notes: containingSpan.notes || getNotesForDate(date),
            type: "blocked",
          } as Block,
        ];
      } else {
        // Fallback to old method if no span found
        const notes = getNotesForDate(date);
        return [
          {
            listingUID: listing.uid,
            notes,
            start_date: dateKey,
            end_date: dateKey,
            type: "blocked",
          } as Block,
        ];
      }
    }

    return [];
  };

  // NEW: Enhanced booking-specific slant analysis for individual dates
  const analyzeBookingSlantForDate = (
    date: Date,
    allListingBookings: Booking[]
  ): {
    hasCheckIn: boolean;
    hasCheckOut: boolean;
    checkInBooking?: Booking;
    checkOutBooking?: Booking;
  } => {
    const dateKey = formatDateKey(date);
    let hasCheckIn = false;
    let hasCheckOut = false;
    let checkInBooking: Booking | undefined;
    let checkOutBooking: Booking | undefined;

    // Remove duplicates by creating a unique booking map
    const uniqueBookings = new Map<string, Booking>();
    allListingBookings.forEach((booking) => {
      const uniqueKey = `${booking.guestName}-${booking.channel}-${booking.arrival_date}-${booking.departure_date}-${booking.amount}`;
      uniqueBookings.set(uniqueKey, booking);
    });

    const uniqueBookingArray = Array.from(uniqueBookings.values());

    uniqueBookingArray.forEach((booking) => {
      const normalizedArrivalDate = normalizeDateFormat(booking.arrival_date);
      const normalizedDepartureDate = normalizeDateFormat(
        booking.departure_date
      );

      // Check for check-in (arrival)
      if (normalizedArrivalDate === dateKey) {
        hasCheckIn = true;
        checkInBooking = booking;
      }

      // Check for check-out (departure)
      if (normalizedDepartureDate === dateKey) {
        hasCheckOut = true;
        checkOutBooking = booking;
      }
    });

    const result = { hasCheckIn, hasCheckOut, checkInBooking, checkOutBooking };
    return result;
  };

  // Enhanced function to analyze slant status for each individual date in a group
  const analyzeGroupSlantStatus = (
    groupDates: Date[]
  ): IndividualDateAnalysis[] => {
    // Get all bookings for this listing once
    const allListingBookings = getAllBookingsForSlantingAnalysis(groupDates[0]);

    const slantAnalysis = groupDates.map((date, index) => {
      const dateKey = formatDateKey(date);
      const blocks = getBlocksForDate(date);
      const rate = getRateForDate(date);

      // Perform booking-specific slant analysis
      const bookingSlantAnalysis = analyzeBookingSlantForDate(
        date,
        allListingBookings
      );

      return {
        date,
        dateKey,
        index,
        allBookings: allListingBookings,
        blocks,
        rate,
        // Enhanced booking analysis
        hasCheckIn: bookingSlantAnalysis.hasCheckIn,
        hasCheckOut: bookingSlantAnalysis.hasCheckOut,
        checkInBooking: bookingSlantAnalysis.checkInBooking,
        checkOutBooking: bookingSlantAnalysis.checkOutBooking,
      } as IndividualDateAnalysis;
    });

    return slantAnalysis;
  };

  const groupConsecutiveDates = () => {
    const groups: Array<{
      type: "booking" | "blocked" | "available";
      startIndex: number;
      endIndex: number;
      data?: Booking | Block | null;
      dates: Date[];
    }> = [];

    let currentGroup: any = null;

    actualVisibleDates.forEach((date, index) => {
      const booking = getBookingForDate(date);
      const isBlocked = isDateBlocked(date);

      // SOLUTION: Check if this is a checkout day that should be treated as available
      // but still handle slant separately via individualDateAnalysis
      if (!booking) {
        // Check if this date is a checkout day for any booking
        const dateKey = formatDateKey(date);
        const isCheckoutDay = bookings.some((b) => {
          if (b.listingUID !== listing.uid) return false;
          const normalizedDeparture = normalizeDateFormat(b.departure_date);
          return normalizedDeparture === dateKey;
        });
      }

      if (booking) {
        const currentBookingArrival = normalizeDateFormat(booking.arrival_date);
        const currentBookingDeparture = normalizeDateFormat(
          booking.departure_date
        );
        const groupDataArrival = normalizeDateFormat(
          currentGroup?.data?.arrival_date
        );
        const groupDataDeparture = normalizeDateFormat(
          currentGroup?.data?.departure_date
        );

        if (
          currentGroup?.type === "booking" &&
          currentGroup.data?.guestName === booking.guestName &&
          currentGroup.data?.channel === booking.channel &&
          groupDataArrival === currentBookingArrival &&
          groupDataDeparture === currentBookingDeparture
        ) {
          currentGroup.endIndex = index;
          currentGroup.dates.push(date);
        } else {
          if (currentGroup) {
            groups.push(currentGroup);
          }
          currentGroup = {
            type: "booking",
            startIndex: index,
            endIndex: index,
            data: booking,
            dates: [date],
          };
        }
      } else if (isBlocked) {
        // Get the actual block span for this date
        const dateKey = formatDateKey(date);
        const actualBlockSpan = blockSpans.find((span) =>
          span.dates.includes(dateKey)
        );

        if (currentGroup?.type === "blocked") {
          currentGroup.endIndex = index;
          currentGroup.dates.push(date);
          // Update with actual block span data if available
          if (currentGroup.data && actualBlockSpan) {
            (currentGroup.data as Block).start_date = actualBlockSpan.start;
            (currentGroup.data as Block).end_date = actualBlockSpan.end;
          }
        } else {
          if (currentGroup) groups.push(currentGroup);

          // Create block data using actual span information
          let blockData: Block | null = null;
          if (actualBlockSpan) {
            blockData = {
              listingUID: listing.uid,
              start_date: actualBlockSpan.start,
              end_date: actualBlockSpan.end,
              notes: actualBlockSpan.notes || getNotesForDate(date),
              type: "blocked",
            } as Block;
          } else {
            // Fallback to old method if no span found
            const notes = getNotesForDate(date);
            blockData = notes
              ? ({
                  listingUID: listing.uid,
                  notes,
                  start_date: formatDateKey(date),
                  end_date: formatDateKey(date),
                  type: "blocked",
                } as Block)
              : null;
          }

          currentGroup = {
            type: "blocked",
            startIndex: index,
            endIndex: index,
            data: blockData,
            dates: [date],
          };
        }
      } else {
        // SOLUTION: This handles both regular available dates AND checkout days
        // Checkout days are now excluded from booking groups (due to getBookingForDate fix)
        // but their slant styling will be handled via individualDateAnalysis
        if (currentGroup) {
          groups.push(currentGroup);
          currentGroup = null;
        }
        groups.push({
          type: "available",
          startIndex: index,
          endIndex: index,
          data: null,
          dates: [date],
        });
      }
    });

    if (currentGroup) {
      groups.push(currentGroup);
    }
    return groups;
  };

  const dateGroups = groupConsecutiveDates();

  return (
    <div
      className={`flex hover:bg-gray-50 transition-colors duration-150 ${isLast ? "" : "border-b border-gray-300"}`}
      style={{
        minWidth: `${listingColumnWidth + visibleDates.length * 120}px`,
      }}
    >
      {/* Listing Info - Sticky Column */}
      <div
        className="px-4 py-3 border-r border-gray-300 bg-white group sticky left-0"
        style={{
          width: `${listingColumnWidth}px`,
          boxShadow: "2px 0 4px rgba(0, 0, 0, 0.1)",
          zIndex: 20,
        }}
      >
        <div className="flex items-center h-10">
          <div
            className="font-medium text-sm truncate cursor-pointer hover:text-blue-600 transition-colors w-full"
            title={listing.name}
          >
            {listing.name}
          </div>
        </div>
        {/* Resize handle */}
        <div
          className="absolute right-0 top-0 bottom-0 w-2 cursor-col-resize hover:bg-blue-200 opacity-0 group-hover:opacity-100 transition-opacity"
          onMouseDown={onResizeStart}
          title="Drag to resize column"
        />
      </div>

      {/* Calendar Cells */}
      <div className="flex-1 flex transition-all duration-300 ease-in-out">
        {dateGroups.map((group, groupIndex) => {
          const spanWidth = group.endIndex - group.startIndex + 1;

          if (group.type === "booking") {
            // Enhanced booking analysis for each individual date in the group
            const slantAnalysis = analyzeGroupSlantStatus(group.dates);

            const isStart = true;
            const isEnd = true;
            const showText = true;

            return (
              <div
                key={groupIndex}
                className="flex relative"
                style={{ flex: spanWidth }}
              >
                {group.dates.map((_, dateIndex) => (
                  <div
                    key={dateIndex}
                    className="absolute h-full"
                    style={{
                      left: `${(dateIndex / spanWidth) * 100}%`,
                      width: `${100 / spanWidth}%`,
                      pointerEvents: "none",
                      zIndex: 10,
                    }}
                  />
                ))}

                <div className="w-full relative z-0">
                  <CalendarCell
                    date={group.dates[0]}
                    booking={
                      group.data && "listingUID" in group.data
                        ? (group.data as Booking)
                        : undefined
                    }
                    bookings={getAllBookingsForSlantingAnalysis(group.dates[0])}
                    blocks={[]} // Add empty blocks array for booking case
                    rate={getRateForDate(group.dates[0])}
                    isBlocked={false}
                    isGrouped={true}
                    groupSpan={spanWidth}
                    groupDates={group.dates}
                    groupData={group.data || undefined}
                    individualDateAnalysis={slantAnalysis}
                    isBookingStart={isStart}
                    isBookingEnd={isEnd}
                    showBookingText={showText}
                    cellIndex={groupIndex}
                    propertyName={listing.name}
                    previousDateRate={getPreviousDateRate(group.dates[0])}
                    hasPreviousAvailability={hasPreviousDateAvailability(
                      group.dates[0]
                    )}
                  />
                </div>
              </div>
            );
          } else if (group.type === "blocked") {
            // Enhanced block analysis for each individual date in blocked group
            const slantAnalysis = analyzeGroupSlantStatus(group.dates);

            const showText = spanWidth === 1 ? true : false;

            return (
              <div
                key={groupIndex}
                className="flex relative"
                style={{ flex: spanWidth }}
              >
                {/* Create individual date borders while maintaining span */}
                {group.dates.map((_, dateIndex) => (
                  <div
                    key={dateIndex}
                    className="flex-1 absolute h-full"
                    style={{
                      left: `${(dateIndex / spanWidth) * 100}%`,
                      width: `${100 / spanWidth}%`,
                      pointerEvents: "none",
                      zIndex: 10,
                    }}
                  />
                ))}

                <div className="w-full relative z-0"> 
                  <CalendarCell
                    date={group.dates[0]}
                    booking={undefined}
                    bookings={getAllBookingsForSlantingAnalysis(group.dates[0])}
                    blocks={group.data ? [group.data as Block] : []}
                    rate={getRateForDate(group.dates[0])}
                    isBlocked={true}
                    isGrouped={true}
                    groupSpan={spanWidth}
                    groupDates={group.dates}
                    groupData={group.data || undefined}
                    // Enhanced individual date analysis
                    individualDateAnalysis={slantAnalysis}
                    notes={
                      group.data && "notes" in group.data
                        ? group.data.notes
                        : undefined
                    }
                    isBookingStart={false}
                    isBookingEnd={false}
                    showBookingText={showText}
                    cellIndex={groupIndex} 
                    propertyName={listing.name}
                    previousDateRate={getPreviousDateRate(group.dates[0])}
                    hasPreviousAvailability={hasPreviousDateAvailability(
                      group.dates[0]
                    )}
                  />
                </div>
              </div>
            );
          } else {
            const slantAnalysis = analyzeGroupSlantStatus(group.dates);

            return group.dates.map((date, dateIndex) => (
              <div
                key={`${groupIndex}-${dateIndex}`}
                className="flex-1 border-l-0"
              >
                <CalendarCell
                  date={date}
                  booking={undefined}
                  bookings={getAllBookingsForSlantingAnalysis(date)}
                  blocks={[]} // Add empty blocks array for availability case
                  rate={getRateForDate(date)}
                  isBlocked={false}
                  isGrouped={false}
                  groupSpan={1}
                  groupDates={[date]}
                  individualDateAnalysis={slantAnalysis}
                  isBookingStart={false}
                  isBookingEnd={false}
                  propertyName={listing.name}
                  cellIndex={groupIndex}
                  previousDateRate={getPreviousDateRate(group.dates[0])}
                  hasPreviousAvailability={hasPreviousDateAvailability(
                    group.dates[0]
                  )}
                />
              </div>
            ));
          }
        })}
      </div>
    </div>
  );
};

export default ListingRow;
