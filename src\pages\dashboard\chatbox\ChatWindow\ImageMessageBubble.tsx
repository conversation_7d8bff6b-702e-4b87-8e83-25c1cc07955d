import { useState } from "react";
import Modal from "@mui/material/Modal";
import Box from "@mui/material/Box";
import Tooltip from "@mui/material/Tooltip";
import Avatar from "@mui/material/Avatar";
import SmartToyOutlinedIcon from "@mui/icons-material/SmartToyOutlined";
import CloseIcon from "@mui/icons-material/Close";
import IconButton from "@mui/material/IconButton";
import { getRandomColor } from "../utils";
import dayjs from "dayjs";
import Slider from "@mui/material/Slider";

const FALLBACK_IMAGE =
  "https://via.placeholder.com/300x200?text=Image+Not+Available";

const ImageMessageBubble = ({
  message,
  convName,
}: {
  message: any;
  convName: string | undefined;
}) => {
  const [open, setOpen] = useState(false);
  const [imgError, setImgError] = useState(false);
  const [zoom, setZoom] = useState(1);

  const isGuest = message.sender === "guest";
  const senderName = isGuest ? convName : message.sender_name;
  const avatarColor =
    senderName === "AI_Bot" ? "#A1E3F9" : getRandomColor(senderName);

  return (
    <>
      <div
        className={`flex ${isGuest ? "justify-start" : "justify-end"} px-2 py-2`}
      >
        {/* Left Avatar */}
        {isGuest && (
          <div className="mr-2 mt-auto">
            <Tooltip title={senderName} placement="top">
              <Avatar
                sx={{
                  bgcolor: avatarColor,
                  color: "white",
                  width: 32,
                  height: 32,
                  fontSize: "0.9rem",
                  fontWeight: "bold",
                }}
              >
                {senderName.charAt(0)}
              </Avatar>
            </Tooltip>
          </div>
        )}

        {/* Image bubble */}
        <div
          className={`relative max-w-[220px] max-h-[180px] sm:max-w-[300px] sm:max-h-[220px] rounded-xl border font-semibold mb-2 overflow-hidden cursor-pointer transition-shadow duration-200 hover:shadow-lg ${
            isGuest
              ? "bg-[#f1f1f1] border-gray-100"
              : "bg-[#c8d1faba] border-gray-200"
          }`}
          onClick={() => !imgError && setOpen(true)}
        >
          {!imgError ? (
            <img
              src={message.content}
              alt="Uploaded"
              onError={() => setImgError(true)}
              className="w-full h-[180px] object-cover rounded-xl p-1"
            />
          ) : (
            <div className="w-32 h-[180px] bg-gray-300 flex text-center items-center justify-center text-sm text-gray-600 rounded-xl p-2">
              <span>Image could not be loaded</span>
            </div>
          )}
          <p className="text-xs text-right mt-1 pr-2 pb-1 text-gray-500">
            {dayjs(
              message.sender === "guest" ? message.received_at : message.sent_at
            ).format("h:mm A")}
          </p>
        </div>

        {/* Right Avatar */}
        {!isGuest && (
          <div className="ml-2 mt-auto">
            <Tooltip
              title={senderName === "AI_Bot" ? "Chatbot" : senderName}
              placement="top"
            >
              <Avatar
                sx={{
                  bgcolor: avatarColor,
                  color: "white",
                  width: 32,
                  height: 32,
                  fontSize: "0.9rem",
                  fontWeight: "bold",
                }}
              >
                {senderName === "AI_Bot" ? (
                  <SmartToyOutlinedIcon />
                ) : (
                  senderName.charAt(0)
                )}
              </Avatar>
            </Tooltip>
          </div>
        )}
      </div>

      {/* Fullscreen Modal */}
      <Modal open={open} onClose={() => setOpen(false)}>
        <Box
          className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center p-4 z-50"
          sx={{ outline: "none", position: "relative" }}
        >
          {/* Close Button */}
          <IconButton
            onClick={() => setOpen(false)}
            sx={{
              position: "fixed",
              top: 20,
              right: 20,
              color: "white",
              backgroundColor: "rgba(255,255,255,0.08)",
              backdropFilter: "blur(5px)",
              zIndex: 100,
              "&:hover": {
                backgroundColor: "rgba(255,255,255,0.2)",
              },
            }}
          >
            <CloseIcon fontSize="medium" />
          </IconButton>

          {/* Zoomable Image */}
          <img
            src={imgError ? FALLBACK_IMAGE : message.content}
            alt="Full Size"
            onError={() => setImgError(true)}
            className="max-w-screen max-h-screen object-contain rounded-md shadow-2xl transition-transform duration-200 ease-in-out"
            style={{
              transform: `scale(${zoom})`,
            }}
          />

          {/* Zoom Slider */}
          <Box
            className="absolute"
            sx={{
              position: "fixed",
              bottom: 20,
              right: 20,
              width: 150,
              padding: "6px 12px",
              backgroundColor: "rgba(255,255,255,0.1)",
              backdropFilter: "blur(8px)",
              borderRadius: "8px",
            }}
          >
            <Slider
              value={zoom}
              onChange={(_, val) => setZoom(val as number)}
              step={0.1}
              min={0.5}
              max={2}
              size="small"
              aria-label="Zoom"
              sx={{
                color: "#ffffff",
              }}
            />
          </Box>
        </Box>
      </Modal>
    </>
  );
};

export default ImageMessageBubble;
