import { useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button, Typography } from "@mui/material";
import { useSelector } from "react-redux";
import { RootState } from "../../redux/store";
import ScheduleDemo from "./scheduleDemo";
import hostTempo from "../../../src/assets/media/hostTempo.png";
import hostAway from "../../../src/assets/media/hostAway.jpg";
import hostFully from "../../../src/assets/media/hostFully.png";
import lodgify from "../../../src/assets/media/lodgify.png";
import aboutUs from "../../../src/assets/media/about-us.png";
import Footer from "./footer";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import FeaturesSection from "./Features";

const Home = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const navigate = useNavigate();
  const formSectionRef = useRef<HTMLDivElement>(null);

  const scrollToForm = () => {
    formSectionRef.current?.scrollIntoView({ behavior: "smooth" }); // Smooth scrolling
  };

  const logos = [
    { src: hostTempo, alt: "flipkey" },
    { src: hostAway, alt: "booking.com" },
    { src: hostFully, alt: "agoda" },
    { src: lodgify, alt: "belvilla" },
  ];

  const { ref, inView } = useInView({
    triggerOnce: true, // Animates only once when in view
    threshold: 0.2, // Triggers when 20% of the component is visible
  });

  return (
    <>
      <div className="w-full mx-auto h-full"  style={{ fontFamily: "sans-serif" }}>
        <div className="fixed top-0 z-50 bg-white shadow-md min-w-full inline-flex px-6">
          <div className="place-content-start inline-flex w-full">
            <div>
              <Link to="/" onClick={() => window.scrollTo(0, 0)}>
                <img
                  src="vividity main.png"
                  height={200}
                  width={180}
                  className="pt-2"
                />
              </Link>
            </div>
          </div>
          <div className="flex items-center justify-between px-4 py-2">
            {/* Left Side Navigation */}
            <div className="flex items-center space-x-6">
              <Link
                to="#home-section"
                className="text-gray-700 font-semibold hover:text-blue-700 py-1 text-nowrap"
                onClick={(e) => {
                  e.preventDefault(); // Prevent default link behavior
                  const section = document.getElementById("home-section");
                  section?.scrollIntoView({ behavior: "smooth" });
                }}
              >
                Home
              </Link>
              <Link
                to="#about-section"
                className="text-gray-700 font-semibold hover:text-blue-700 py-1 text-nowrap"
                onClick={(e) => {
                  e.preventDefault(); // Prevent default link behavior
                  const section = document.getElementById("about-section");
                  section?.scrollIntoView({ behavior: "smooth" });
                }}
              >
                About Us
              </Link>
              <Link
                to="#contact-section"
                className="text-gray-700 font-semibold hover:text-blue-700 py-1 text-nowrap pr-5"
                onClick={(e) => {
                  e.preventDefault(); // Prevent default link behavior
                  const section = document.getElementById("contact-section");
                  section?.scrollIntoView({ behavior: "smooth" });
                }}
              >
                Contact Us
              </Link>
            </div>

            {/* Right Side Button */}
            <div className="text-white rounded-lg font-bold my-auto px-2 py-2">
              {accessToken ? (
                <Button
                  onClick={() => navigate("/dashboard/home")}
                  className="!text-white px-3 text-nowrap !font-semibold !mr-6"
                  style={{ backgroundColor: "#0066b2" }}
                >
                  Go to Dashboard
                </Button>
              ) : (
                <Button
                  onClick={() => navigate("/login")}
                  className="!text-white px-3 text-nowrap !font-semibold !mr-6"
                  style={{ backgroundColor: "#0066b2" }}
                >
                  Login
                </Button>
              )}
            </div>
          </div>
        </div>
        <div>
          {/*                home section                        */}

          <div
            id="home-section"
            className="grid grid-cols-2 gap-5 w-full mx-auto h-screen items-center px-16"
          >
            <div className="text-center px-4 pt-10">
              <h1 className="text-4xl pb-5 font-medium text-left">
                Maximize Your{" "}
                <span className="" style={{ color: "#0066b2" }}>
                  <b>Revenue </b>
                </span>{" "}
                Potential with Our Innovative Solution
              </h1>
              <p className="text-lg font-thin text-left">
                Empower your business with intelligent pricing, real-time data,
                and comprehensive reporting. Vividity helps you make data-driven
                decisions that maximize your revenue and streamline operations.
              </p>
              <div className="flex pl-4 inline-flex text-white mt-8 py-2 font-bold mx-0 w-full justify-start">
                <Button
                  onClick={scrollToForm}
                  className="!text-white px-3 text-nowrap !font-semibold !capitalize"
                  style={{ backgroundColor: "#0066b2" }}
                >
                  Schedule a demo
                </Button>
                <Link
                  type="submit"
                  color="inherit"
                  className="px-5 py-3 bg-blue-100 rounded-lg shadow-lg ml-3 text-blue-800 border"
                  to={""}
                >
                  Documentation
                </Link>
              </div>
            </div>
            <div className="">
              <img
                src="banner2.png" className="h-screen ml-auto py-12 w-full object-contain"
              ></img>
            </div>
          </div>

          {/*                about section                         */}

          <section
            id="about-section"
            className="h-screen content-center"
          >
            <div className="max-w-7xl mx-auto text-center">
              {/* <p
                className="text-2xl shadow-stone-950 text-justify"
                style={{ lineHeight: "2" }}
              >
                Connects with Open APIs of multiple Property Management Systems
                (PMS) to retrieve key data like reservations, property details,
                and availability.
              </p> */}
            </div>
            <FeaturesSection />
          </section>

          {/*               screeshot section                      */}

          <section className="relative flex h-screen w-4/5 mx-auto overflow-hidden mb-24">
            {/* Left Side: First Image */}
            <div className="w-2/3 h-full relative">
              <img
                src="screenshot-home.png"
                className="w-full h-full object-cover"
                alt="Home Screenshot"
              />

              <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-white to-transparent pointer-events-none"></div>
              {/* Fade out effect at bottom */}
              <div className="absolute top-0 right-0 h-full w-24 bg-gradient-to-l from-white to-transparent pointer-events-none"></div>
            </div>

            {/* Right Side: Second and Third Images */}
            <div className="w-1/2 flex flex-col justify-between relative -ml-12">
              {" "}
              {/* -ml-12 causes overlap */}
              <img
                src="screenshot-perf.png"
                className="w-full h-fit object-cover mb-4 rounded-lg shadow-lg"
                alt="Performance Screenshot"
              />
              <img
                src="screenshot-channel.png"
                className="w-full h-fit object-cover rounded-lg shadow-lg bg-gradient-to-t from-white to-transparent pointer-events-none"
                alt="Channel Screenshot"
              />
            </div>
          </section>

          <Typography
            variant="h4"
            sx={{
              textAlign: "center",
              fontWeight: 400,
              color: "GrayText",
              lineHeight: 3,
            }}
            ref={ref}
            style={{ fontFamily: "sans-serif" }}
          >
            API Integration services to leading travel and hospitality companies
          </Typography>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 justify-items-center pb-5 px-5">
            {logos.map((logo, index) => (
              <motion.div
                key={index}
                className="grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition duration-500 content-center mx-auto px-5"
                initial={{ y: 50, opacity: 0 }}
                animate={inView ? { y: 0, opacity: 1 } : {}}
                transition={{ duration: 0.5, delay: index * 0.5 }}
              >
                <img
                  src={logo.src}
                  alt={logo.alt}
                  className="items-center m-auto py-4 px-5 cursor-pointer"
                  width="250"
                />
              </motion.div>
            ))}
          </div>

          {/*                 contact section                      */}

          <div ref={formSectionRef} id="contact-section" className="pt-28 ">
            <ScheduleDemo />
          </div>
        </div>

        <div className="h-76 mt-5">
          <Footer />
        </div>
      </div>

      {/* <div
        className="flex inline-flex bg-white text-blue-800 text-sm fixed w-full gap-4 px-2 justify-between items-center"
        style={{ bottom: "0" }}
      >
        <div
          color="inherit"
          className="border-white align-left w-fit font-medium tracking-wider cursor-pointer"
          style={{ fontSize: 12 }}
        >
          vividity.com
        </div>
        <div
          className="text-xs border-slate-50 p-2 text-blue-800 !font-smeibold"
          style={{ fontSize: 10, whiteSpace: "nowrap" }}
        >
          Copyright © Keleno Labs Pvt. Ltd. All rights reserved.
        </div>
      </div> */}
    </>
  );
};

export default Home;
