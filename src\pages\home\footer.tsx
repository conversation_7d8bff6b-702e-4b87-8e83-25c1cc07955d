import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPhone, faEnvelope, faHome } from "@fortawesome/free-solid-svg-icons";
// import { motion } from "framer-motion";
// import { useInView } from "react-intersection-observer";
import {
  faFacebookF,
  faXTwitter,
  faInstagram,
  faLinkedinIn,
  faYoutube,
} from "@fortawesome/free-brands-svg-icons";
import { Typography } from "@mui/material";

const Footer = () => {
  //   const { ref, inView } = useInView({
  //     triggerOnce: true, // Ensures animation runs only once
  //     threshold: 0.4, // Triggers when 20% of the section is visible
  //   });

  return (
    <footer
      className="px-16 w-100 mx-auto rounded-lg mt-12 py-14 pb-5 bg-blue-200 relative b-0"
      style={{ color: "#0066b2", fontFamily: "sans-serif" }}
      //   ref={ref} // Attach ref to track visibility
      //   initial={{ y: 100, opacity: 0 }} // Start from below with opacity 0
      //   animate={inView ? { y: 0, opacity: 1 } : { y: 100, opacity: 0 }} // Smooth transition
      //   transition={{ duration: 0.7, ease: "easeOut" }} // Adds smooth easing
    >
      <div className="mx-auto">
        <img
          src="vividity main.png"
          height={200}
          width={180}
          className="pt-2"
        ></img>
      </div>
      <div className="mx-auto grid grid-cols-3 gap-10 space-y-16">
        {/* Logo & Industries */}
        <div className="pt-12">
          <h3 className="font-bold mt-4">Industries</h3>
          <ul className="mt-2 space-y-3 text-lg">
            <li className="">- Travel</li>
            <li>- Hospitality</li>
          </ul>

          <h3 className="font-bold mt-4">Company</h3>
          <ul className="mt-2 space-y-3 text-lg">
            <li>- Terms & Conditions</li>
            <li>- Privacy Policy</li>
          </ul>
        </div>

        {/* Resources & Team */}
        <div>
          <h3 className="font-bold">Resources</h3>
          <ul className="mt-2 space-y-3 text-lg">
            <li>- Whitepapers</li>
            <li>- Case Studies</li>
          </ul>

          <h3 className="font-bold mt-4">Team</h3>
          <ul className="mt-2 space-y-3 text-lg">
            {/* <li>- Careers</li> */}
            <li>- Blog</li>
          </ul>
        </div>

        {/* Contact Section */}
        <div>
          <h3 className="font-bold">Contact</h3>
          <ul className="mt-2 space-y-3 text-lg">
            <li>
              <FontAwesomeIcon
                icon={faHome}
                className="mr-2 cursor-default"
              />
              Keleno LLC Office Address: 157 Millingden Trail, West Lafayette,
              IN, 47906, USA
            </li>
            <li>
              <FontAwesomeIcon
                icon={faPhone}
                className="mr-2 "
              />
              +91 8848 358 534
            </li>
            <li>
              <FontAwesomeIcon
                icon={faEnvelope}
                className="mr-2"
              />
              <EMAIL>
            </li>
          </ul>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="border-t border-blue-500 mt-6 pt-4 flex justify-between mx-auto items-center">
        <h4>Copyright© Vividity.in. All Rights Reserved.</h4>
        <div className="flex justify-start space-x-4">
          <a href="#" className="text-lg">
            <FontAwesomeIcon icon={faFacebookF} />
          </a>
          <a href="#" className="text-lg">
            <FontAwesomeIcon icon={faXTwitter} />
          </a>
          <a href="#" className="text-lg">
            <FontAwesomeIcon icon={faInstagram} />
          </a>
          <a href="#" className="text-lg">
            <FontAwesomeIcon icon={faLinkedinIn} />
          </a>
          <a href="#" className="text-lg">
            <FontAwesomeIcon icon={faYoutube} />
          </a>
        </div>
      </div>
      <style>
        {`
        li {
        cursor: pointer;
        }
        `}
      </style>
    </footer>
  );
};

export default Footer;
