import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

const QuoteDetailsCard = ({ reservationDetails }: any) => {
  const formatAmount = (value?: number) =>
    value !== undefined
      ? `${reservationDetails?.currency || "AED"} ${value.toLocaleString()}`
      : "—";

  return (
    <Accordion className="bg-white shadow-sm rounded-lg">
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="quote-details-content"
        id="quote-details-header"
        className="px-4"
      >
        <Typography className="text-sm font-semibold text-gray-700">
          QUOTE
        </Typography>
      </AccordionSummary>
      <AccordionDetails className="px-4 pb-4 space-y-3 text-sm text-gray-700">
        {[
          { label: "Base Rate", value: reservationDetails?.base_price },
          { label: "Cleaning Fee", value: reservationDetails?.cleaning_fee },
          { label: "VAT", value: reservationDetails?.vat },
          { label: "Tourism Fee", value: reservationDetails?.city_tax },
        ].map((item, index) => (
          <div key={index} className="flex justify-between">
            <p className="text-gray-500">{item.label}</p>
            <p className="text-gray-800">{formatAmount(item.value)}</p>
          </div>
        ))}

        <div className="pt-2 mt-2 border-t text-sm font-semibold text-gray-900 flex justify-between">
          <span>TOTAL PRICE:</span>
          <span>{formatAmount(reservationDetails?.total_price)}</span>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default QuoteDetailsCard;
