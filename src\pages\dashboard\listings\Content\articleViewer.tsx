import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "antd";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Card,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { fetchArticleById } from "../../../../services/targetAPI";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import "./custom-quill.css";

interface Article {
  id: string;
  title: string;
  content: string;
  contentType: string;
  createdAgo: string;
  createdBy: string;
  updatedAgo: string;
  updatedBy: string;
  folder: string;
}

const ArticlePage: React.FC = () => {
  const { articleId } = useParams<{ articleId: string }>();
  const navigate = useNavigate();

  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchArticle = async () => {
      if (!articleId) {
        setError("Article ID not found");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await fetchArticleById(articleId);

        // Map API response to your Article interface
        const articleData: Article = {
          id: response.data.id,
          title: response.data.title,
          content:
            response.data.content_sanitized || response.data.content_raw || "",
          contentType:
            response.data.content_type === "internal_article"
              ? "Internal article"
              : "Public article",
          createdAgo: response.data.formatted_created_at || "Unknown",
          createdBy: response.data.created_by.first_name || "Unknown",
          updatedAgo: response.data.formatted_updated_at || "Unknown",
          updatedBy: response.data.updated_by.first_name || "Unknown",
          folder: response.data.folder_name || response.folder || "No folder",
        };

        setArticle(articleData);
        setError(null);
      } catch (err) {
        console.error("Failed to fetch article", err);
        setError("Failed to load article");
      } finally {
        setLoading(false);
      }
    };

    fetchArticle();
  }, [articleId]);

  if (loading) {
    return <div className="p-6 text-gray-500">Loading article...</div>;
  }
  if (error || !article) {
    return (
      <div className="p-6 text-red-500">
        {error || "Article not found"}
        <Button onClick={() => navigate(-1)} className="ml-4">
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="p-4" style={{ height: "calc(100vh - 65px)" }}>
      <div className="flex flex-row h-full bg-white w-full border rounded-lg p-4">
        {/* Main content */}
        <div className="flex flex-col w-3/4 p-2">
          <div className="flex flex-row w-full h-12 justify-between border-b pb-4 mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              {article.contentType}
            </h2>
            <div className="space-x-2">
              <Button onClick={() => navigate(-1)} className="!border-gray-300">
                Cancel
              </Button>
              <Button
                type="primary"
                className="!bg-black !text-white"
                onClick={() =>
                  navigate(`/dashboard/listings/articles/edit/${articleId}`)
                }
              >
                Edit
              </Button>
            </div>
          </div>

          <div className="lg:col-span-3 h-full overflow-hidden">
            <div className="h-full overflow-y-auto pr-4">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {article.title}
              </h1>
              <ReactQuill
                value={article.content}
                readOnly={true}
                theme="snow"
                modules={{ toolbar: false }}
                style={{ border: "none" }}
                className="quill-viewer"
              />
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="w-1/4 space-y-4 border-l items-center p-2">
          {/* Data Accordion */}
          <div className="pb-4 mb-6 border-b h-12">
            <h3 className="text-lg font-semibold text-gray-900 ">Details</h3>
          </div>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography className="!font-bold text-gray-900 text-md">
                Data
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <div className="space-y-2 !font-small !text-sm !text-gray-700">
                <div>
                  <span className="!font-semibold !text-gray-800">Type:</span>{" "}
                  {article.contentType}
                </div>
                <div>
                  <span className="!font-semibold !text-gray-800">
                    Created:
                  </span>{" "}
                  {article.createdAgo}
                </div>
                <div>
                  <span className="!font-semibold !text-gray-800">
                    Created by:
                  </span>{" "}
                  {article.createdBy}
                </div>
                <div>
                  <span className="!font-semibold !text-gray-800">
                    Last updated:
                  </span>{" "}
                  {article.updatedAgo}
                </div>
                <div>
                  <span className="!font-semibold !text-gray-800">
                    Last updated by:
                  </span>{" "}
                  {article.updatedBy}
                </div>
              </div>
            </AccordionDetails>
          </Accordion>

          {/* Folder Accordion */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography className="!font-bold !text-gray-900 !text-md">
                Folder
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <p className="!text-sm !text-gray-700">{article.folder}</p>
            </AccordionDetails>
          </Accordion>
        </div>
      </div>

    </div>
  );
};

export default ArticlePage;
