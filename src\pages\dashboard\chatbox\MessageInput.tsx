import { useState,useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import { addMessage } from "../../../redux/chatSlice";
import { Send } from "@mui/icons-material";
import QuestionAnswerOutlinedIcon from "@mui/icons-material/QuestionAnswerOutlined";
import { createManualReply } from "../../../services/chatAPI";

const MessageInput = () => {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const selectedConversation = useSelector(
    (state: RootState) => state.chat.selectedConversation
  );

  const handleSendMessage = async () => {
    if (!selectedConversation || !message.trim() || isSending) return;
    const messageContent = message.trim();
    const newMessage = {
      id: new Date().toISOString(),
      sender: "ai",
      content: message,
      conversationId: selectedConversation,
      timestamp: new Date().toISOString(),
    };
    try {
      setIsSending(true);
      setMessage("");
        const textarea = document.querySelector('textarea');
      if (textarea) {
        textarea.style.height = 'auto';
      }
      await createManualReply(selectedConversation, newMessage?.id, message);
    } catch (error) {
      console.error("Failed to send message:", error);
      setMessage(messageContent);
      const textarea = document.querySelector('textarea');
      if (textarea) {
        textarea.style.height = 'auto';
      }
    } finally {
      setIsSending(false);
    }
  };

  // const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
  //   if (e.key === "Enter") {
  //     handleSendMessage();
  //   }
  // };
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
  if (e.key === "Enter" && !e.shiftKey) {
    e.preventDefault();
    handleSendMessage();
  }
};
  useEffect(() => {
    setMessage("");
  }, [selectedConversation]);

  return (
  <div className="w-full flex items-center px-4 bg-white">
    <QuestionAnswerOutlinedIcon className="text-gray-500 mr-2" />
    <div className="flex-1 flex items-center px-3 py-2 border border-gray-300 shadow rounded-full">
      <textarea
          className="flex-1 bg-transparent outline-none text-gray-800 resize-none overflow-y-auto max-h-[3rem] min-h-[1.5rem] leading-6 mx-2"
          placeholder="Type a message..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyPress}
          disabled={isSending}
          rows={1}
          style={{
            height: 'auto',
            maxHeight: '3rem'
          }}
          onInput={(e: React.FormEvent<HTMLTextAreaElement>) => {
            const target = e.target as HTMLTextAreaElement;
            target.style.height = 'auto';
            target.style.height = Math.min(target.scrollHeight, 48) + 'px';
          }}
        />
      <button
        className={`text-white p-2 rounded-full transition-all duration-200 ${
          isSending || !message.trim()
            ? "bg-gray-400 cursor-not-allowed"
            : "bg-black hover:bg-gray-700"
        }`}
        onClick={handleSendMessage}
        disabled={isSending || !message.trim()}
      >
        <Send fontSize="small" />
      </button>
    </div>
  </div>
);
};

export default MessageInput;
