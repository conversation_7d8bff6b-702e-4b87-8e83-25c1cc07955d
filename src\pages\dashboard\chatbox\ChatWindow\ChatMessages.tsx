import { useLayoutEffect, useRef } from "react";
import { Chip } from "@mui/material";
import dayjs from "dayjs";
import MessageBubble from "./MessageBubble";
import AITypingIndicator from "./AITypingIndicator";
import ImageMessageBubble from "./ImageMessageBubble";

const formatDate = (date: string) => {
  const today = dayjs().startOf("day");
  const messageDate = dayjs(date).startOf("day");

  if (messageDate.isSame(today)) return "Today";
  if (messageDate.isSame(today.subtract(1, "day"))) return "Yesterday";
  return dayjs(date).format("MMM D, YYYY");
};

const ChatMessages = ({
  messages,
  isGenerating,
  setModalOpen,
  handleOpenModal,
  convName,
}: {
  messages: any[];
  isGenerating: boolean;
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleOpenModal:(message: any) => void;
  convName: string | undefined;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom before the browser repaints
  useLayoutEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [messages, isGenerating]);

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-y-auto p-4 bg-white"
    >
      {messages.reduce((acc: any[], msg, index) => {
        const currentDate =
          msg.sender === "guest"
            ? formatDate(msg.received_at)
            : formatDate(msg.sent_at);
        const previousDate =
          index > 0 ? formatDate(messages[index - 1].timestamp) : null;

        if (currentDate !== previousDate) {
          acc.push(
            <div key={`date-${currentDate}`} className="flex justify-center">
              <Chip label={currentDate} size="small" className="text-sm !bg-white font-semibold !text-gray-500" />
            </div>
          );
        }

        if (msg.is_image) {
          acc.push(
            <ImageMessageBubble
              key={msg.id}
              message={msg}
              convName={convName}
            />
          );
        } 
         else {
          acc.push(
            <MessageBubble
              key={msg.id}
              message={msg}
              setModalOpen={setModalOpen}
              handleOpenModal={handleOpenModal}
              convName={convName}
            />
          );
        }
        return acc;
      }, [])}

      {isGenerating && <AITypingIndicator />}
    </div>
  );
};

export default ChatMessages;
