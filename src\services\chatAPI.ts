import apiClient from "./apiClient";
import { toast } from "react-toastify";

export const fetchConversations = async (
  page = 1,
  pageSize = 20,
  search = "",
  types: string[] = []
) => {
  const params = new URLSearchParams();
  params.append("search", search);
  params.append("page", String(page));
  params.append("page_size", String(pageSize));
  types.forEach((t) => params.append("type", t));

  return apiClient.get(`/api/conversations/?${params.toString()}`);
};

export const fetchMessageByID = (conversationID: string) =>
  apiClient.get(`/api/messages/${conversationID}/`);

export const fetchDetailsByID = (conversationID: string) =>
  apiClient.get(`/api/propertyDetails/${conversationID}/`);

export const approveAIReply = async (
  replyId: string | number,
  content: string,
  declined = false
) => {
  return await apiClient.post("/api/approve-ai-reply/", {
    reply_id: replyId,
    content,
    declined,
  });
};

export const createManualReply = async (
  conversationId: string | number,
  messageId: string | number,
  content: string
) => {
  try {
    const response = await apiClient.post("/api/create-reply/", {
      conversation_id: conversationId,
      message_id: messageId,
      content,
    });
    
    // Show success toast since backend returns plain string
    toast.success("Message sent successfully!");
    
    return response;
  } catch (error) {
    // Error toast will be handled by apiClient interceptor
    toast.error("Message not sent!");
  }
};

export const translateToEn = (text: string, src: string) =>
  apiClient.post("/api/message/translate/", {
    text,
    src,
  });
