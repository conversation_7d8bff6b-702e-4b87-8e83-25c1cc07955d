import { Select, Tag, Space } from "antd";
import type { FC } from "react";
import { Box } from "@mui/material";
import { useRef, useEffect } from "react";

const { Option } = Select;

interface ConversationFiltersProps {
  selectedFilters: string[];
  isLoading: boolean;
  onFilterChange: (value: string[]) => void;
}

const FILTER_OPTIONS = [
  { value: "reply", label: "Reply Needed" },
  { value: "inquiry", label: "Inquiries" },
  { value: "cancelled", label: "Cancelled" },
  { value: "upcoming", label: "Upcoming" },
  { value: "current", label: "Current" },
  { value: "past", label: "Past" },
];

const ConversationFilters: FC<ConversationFiltersProps> = ({
  selectedFilters,
  onFilterChange,
  isLoading,
}) => {
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  const handleChange = (values: string[]) => {
    onFilterChange(values);
  };

  const getLabel = (value: string) =>
    FILTER_OPTIONS.find((f) => f.value === value)?.label || value;

  const renderTags = () => {
    const tagsToShow = selectedFilters;

    return tagsToShow.map((filter) => (
      <Tag
        key={filter}
        style={{
          backgroundColor: "#fff",
          border: "1px solid #d9d9d9",
          fontSize: 12,
          padding: "2px 8px",
        }}
      >
        {getLabel(filter)}
      </Tag>
    ));
  };

  // Force dropdown width
  useEffect(() => {
    const observer = new MutationObserver(() => {
      if (dropdownRef.current) {
        dropdownRef.current.style.width = "160px";
      }
    });

    const interval = setInterval(() => {
      const dropdown = document.querySelector(
        ".ant-select-dropdown"
      ) as HTMLDivElement;
      if (dropdown) {
        dropdownRef.current = dropdown;
        dropdown.style.width = "160px";
        clearInterval(interval);
      }
    }, 50);

    return () => {
      observer.disconnect();
      clearInterval(interval);
    };
  }, []);

  return (
    <Box
      display="flex"
      alignItems="center"
      justifyContent="space-between"
      gap={2}
      flexWrap="wrap"
    >
      {/* Dropdown on the left */}
      <Box sx={{ minWidth: 100 }}>
        <Select
          mode="multiple"
          value={selectedFilters}
          onChange={handleChange}
          placeholder="All"
          style={{ width: 100 }}
          dropdownStyle={{ width: 160 }}
          maxTagCount={0}
          allowClear
          disabled={isLoading}
        >
          {FILTER_OPTIONS.map((opt) => (
            <Option key={opt.value} value={opt.value}>
              {opt.label}
            </Option>
          ))}
        </Select>
      </Box>

      {/* Tags aligned right */}
      <Box
        display="flex"
        flexWrap="wrap"
        justifyContent="flex-start"
        gap={1}
        flex="1"
      >
        {renderTags()}
      </Box>
    </Box>
  );
};

export default ConversationFilters;
