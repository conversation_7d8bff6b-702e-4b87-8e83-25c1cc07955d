import React, { useState, useEffect } from "react";
import {
  Button,
  Modal,
  Form,
  Input,
  Select,
  message,
  Alert,
  Typography,
} from "antd";
import {
  PlusOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import {
  createSource,
  validateSourceNameAPI,
  testSourceConnection,
  syncTargetSheetsAPI
} from "../../../../services/targetAPI";

const { Option } = Select;
const { Paragraph, Text } = Typography;

type AddSourceModalProps = {
  loadSources: () => Promise<void>;
};

const AddSourceModal: React.FC<AddSourceModalProps> = ({ loadSources }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();

  const [name, setName] = useState("");
  const [isNameTaken, setIsNameTaken] = useState(false);
  const [checking, setChecking] = useState(false);

  const [testingConnection, setTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<
    null | "success" | "error"
  >(null);
  const [connectionMessage, setConnectionMessage] = useState<string | null>(
    null
  );
  const [isSaveEnabled, setIsSaveEnabled] = useState(false);
  const [formValues, setFormValues] = useState<any>({});

  const handleNameChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
    if (newName.trim() === "") {
      setIsNameTaken(false);
      return;
    }

    setChecking(true);
    try {
      const exists = await validateSourceNameAPI(newName);
      setIsNameTaken(exists.data.exists);
    } catch (error) {
      console.error("Validation error", error);
    } finally {
      setChecking(false);
    }
  };

  const showModal = () => {
    setIsModalOpen(true);
    setConnectionStatus(null);
    setConnectionMessage(null);
    setIsSaveEnabled(false);
    setFormValues({});
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setIsNameTaken(false);
    setConnectionStatus(null);
    setConnectionMessage(null);
    form.resetFields();
  };

  const handleSave = () => {
    form.validateFields().then(async (values) => {
      try {
        await createSource({ payload: JSON.stringify(values) });
        message.success("Source added successfully!");
        try {
        await syncTargetSheetsAPI();
        message.success("Target sheets synced successfully!");
      } catch (syncError) {
        console.error("Sync error:", syncError);
        message.warning("Source saved but failed to sync target sheets.");
      }
        setIsModalOpen(false);
        setIsNameTaken(false);
        setConnectionStatus(null);
        setConnectionMessage(null);
        form.resetFields();
        loadSources();
      } catch (error) {
        console.error("Save error:", error);
        message.error("Failed to save source.");
      }
    });
  };

  const handleTestConnection = async () => {
    try {
      const values = form.getFieldsValue();
      if (values.doc_type === "google_sheet") {
        setTestingConnection(true);
        setConnectionMessage(null);
        const response = await testSourceConnection({
          name: values.name,
          url: values.url,
          doc_type: values.doc_type,
          source_type: values.source_type,
        });

        if (response?.data?.accessible) {
          setConnectionStatus("success");
          setIsSaveEnabled(true);
          setConnectionMessage("Connection successful.");
        } else {
          setConnectionStatus("error");
          setIsSaveEnabled(false);
          setConnectionMessage(
            "Connection failed. Please confirm the document name, URL and ensure access is granted."
          );
        }
      } else {
        setConnectionStatus("error");
        setIsSaveEnabled(false);
        setConnectionMessage(
          "Connection failed. Please use Google sheets as the source."
        );
      }
    } catch (err) {
      setConnectionStatus("error");
      setIsSaveEnabled(false);
      setConnectionMessage(
        "Connection failed. Please confirm the document URL and ensure access is granted."
      );
    } finally {
      setTestingConnection(false);
    }
  };

  const handleClear = () => {
    form.resetFields();
    setConnectionStatus(null);
    setConnectionMessage(null);
    setIsSaveEnabled(false);
    setFormValues({});
  };

  return (
    <div>
      <Button
        type="primary"
        onClick={showModal}
        className="!bg-black !text-white"
        icon={<PlusOutlined />}
      >
        New source
      </Button>

      <Modal
        title={<span className="text-lg font-semibold">Add New Source</span>}
        open={isModalOpen}
        onCancel={handleCancel}
        footer={null}
        centered
        className="rounded-xl"
      >
        {formValues.doc_type === "google_sheet" && (
          <Alert
            type="info"
            showIcon
            icon={<InfoCircleOutlined />}
            message="Before You Begin"
            description={
              <div>
                <ul className="list-disc ml-4 text-sm">
                  <li>
                    <Text strong>For Google Sheets</Text>, ensure the sheet is
                    shared with:{" "}
                    <Text code>
                      <EMAIL>
                    </Text>
                  </li>
                </ul>
              </div>
            }
            className="mb-4"
          />
        )}

        <Form
          layout="vertical"
          form={form}
          className="pt-2"
          onValuesChange={(_, allValues) => setFormValues(allValues)}
        >
          <Form.Item
            label="Document Name"
            name="name"
            help={isNameTaken ? "This source name is already in use." : ""}
            rules={[
              { required: true, message: "Please enter a document name" },
            ]}
            validateStatus={isNameTaken ? "error" : ""}
          >
            <Input
              value={name}
              onChange={handleNameChange}
              placeholder="Enter document name"
            />
          </Form.Item>

          <Form.Item
            label="Source Type"
            name="source_type"
            rules={[{ required: true, message: "Please select a source type" }]}
          >
            <Select placeholder="Select source type">
              <Option value="target">Target</Option>
              <Option value="property_details">Property Details</Option>
              <Option value="faq">Faq</Option>
              <Option value="others">Others</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Document Type"
            name="doc_type"
            rules={[
              { required: true, message: "Please select a document type" },
            ]}
          >
            <Select placeholder="Select type">
              <Option
                value="csv"
                disabled={formValues.source_type === "target"}
              >
                CSV
              </Option>
              <Option
                value="word"
                disabled={formValues.source_type === "target"}
              >
                Word
              </Option>

              <Option value="google_sheet">Google Sheet</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Document URL"
            name="url"
            rules={[
              { required: true, message: "Please enter a URL" },
              { type: "url", message: "Enter a valid URL" },
            ]}
          >
            <div className="flex gap-2 items-center">
              <Input placeholder="https://example.com/document" />
              <Button
                onClick={handleTestConnection}
                loading={testingConnection}
                disabled={!formValues.url}
                type="default"
              >
                Test
              </Button>

              {connectionStatus === "success" && (
                <CheckCircleOutlined className="text-green-500 text-xl" />
              )}
              {connectionStatus === "error" && (
                <CloseCircleOutlined className="text-red-500 text-xl" />
              )}
            </div>
          </Form.Item>

          {connectionMessage && (
            <div className="mb-2 text-sm text-gray-600">
              <Alert
                type={connectionStatus === "success" ? "success" : "error"}
                message={connectionMessage}
                showIcon
              />
            </div>
          )}

          <div className="flex justify-end gap-2 mt-4">
            <Button
              onClick={handleClear}
              className="border-gray-400 text-gray-700"
            >
              Clear
            </Button>
            <Button
              type="primary"
              onClick={handleSave}
              disabled={isNameTaken || !isSaveEnabled || testingConnection}
            >
              Save
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default AddSourceModal;