export interface TableData {
  [x: string]: string | number;
}

export interface Reservation {
  id: number;
  guest: string;
  channel: string;
  booking_date: string;
  check_in_date: string;
  check_out_date: string;
  nights: number;
  reservation_status: string;
  listing: string;
  revenue: number;
  adr: number;
  blocked_nights: number;
  owner_stay: string;
}

export interface ExportReservationType {
  channels: string[];
  listings: string[];
  selectedDate: Dayjs | null;
  selection: string;
}
