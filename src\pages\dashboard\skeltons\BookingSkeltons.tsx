import Skeleton from "@mui/material/Skeleton";
import Box from "@mui/material/Box";

const BookingSkeleton: React.FC = () => {
  return (
    <>
        <div>
          <Box className="flex w-full flex-row gap-2 grid grid-cols-12 gap-2">
              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>
              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>

              <Box>
                  <Skeleton className="p-6" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
                  <Skeleton animation="wave" className="p-1" />
              </Box>
            </Box>
        </div>
        <div className="w-full inline-flex justify-end pt-8">
            <Box className="w-[50px] mr-4">
                <Skeleton className="px-6 py-1" />
            </Box>
            <Box className="w-[300px] text-left">
                <Skeleton className="px-6 py-1" />
            </Box>
        </div>
    </>
  );
};

export default BookingSkeleton;
