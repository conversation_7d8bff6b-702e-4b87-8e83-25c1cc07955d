import { useState, useRef, useEffect } from "react";
import {
  TextField,
  IconButton,
  InputAdornment,
  ClickAwayListener,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import CloseIcon from "@mui/icons-material/Close";
import { styled, alpha } from "@mui/material/styles";

interface ExpandableSearchProps {
  onSearch: (value: string) => void;
  placeholder?: string;
  isLoading: boolean;
  debounceDelay?: number; // Optional prop for debounce delay
}

const CollapsedSearchButton = styled("div")(() => ({
  width: 34,
  height: 34,
  borderRadius: 12,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  transition: "all 0.3s ease",
  backgroundColor: "transparent",
  boxShadow: "none",
  cursor: "pointer",
}));

const SearchWrapper = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  transition: "all 0.3s ease",
  backgroundColor: alpha(theme.palette.grey[100], 0.6),
  borderRadius: 8,
  overflow: "hidden",
  height: 36,
  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  boxShadow: "0 1px 2px rgba(188, 184, 184, 0.39)",
  backdropFilter: "blur(6px)",
  "&:hover": {
    backgroundColor: alpha(theme.palette.grey[200], 0.7),
  },
}));

const StyledInput = styled(TextField)(() => ({
  "& .MuiOutlinedInput-root": {
    paddingRight: 4,
    paddingLeft: 6,
    fontSize: "0.9rem",
    background: "transparent",
    "& fieldset": {
      border: "none",
    },
  },
}));

const ExpandableSearch: React.FC<ExpandableSearchProps> = ({
  onSearch,
  placeholder = "Search conversations...",
  debounceDelay = 1000,
  isLoading,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [value, setValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  // Debounce: delay onSearch until typing stops
  useEffect(() => {
    const handler = setTimeout(() => {
      onSearch(value);
    }, debounceDelay);

    return () => clearTimeout(handler); // Cleanup
  }, [value, debounceDelay, onSearch]);

  useEffect(() => {
    if (expanded && inputRef.current) {
      inputRef.current.focus();
    }
  }, [expanded]);

  const handleToggle = () => {
    if (expanded) {
      setValue("");
      onSearch("");
    }
    setExpanded(!expanded);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
  };

  const handleClickAway = () => {
    if (expanded && value === "") {
      setExpanded(false);
    }
  };

  return (
    <ClickAwayListener
      onClickAway={handleClickAway}
    >
      <SearchWrapper sx={{ width: expanded ? 220 : 36 }}>
        {expanded ? (
          <StyledInput
            fullWidth
            size="small"
            variant="outlined"
            value={value}
            inputRef={inputRef}
            onChange={handleChange}
            placeholder={placeholder}
            disabled={isLoading}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={handleToggle} size="small">
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        ) : (
          <CollapsedSearchButton onClick={handleToggle}>
            <SearchIcon
              sx={{
                fontSize: "20px",
                color: "text.secondary",
              }}
            />
          </CollapsedSearchButton>
        )}
      </SearchWrapper>
    </ClickAwayListener>
  );
};

export default ExpandableSearch;
