import {
  <PERSON>I<PERSON>,
  <PERSON><PERSON>temButton,
  ListItemText,
  Avatar,
  Box,
  Typography,
} from "@mui/material";
import { Tag } from "antd";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import isToday from "dayjs/plugin/isToday";
import isYesterday from "dayjs/plugin/isYesterday";
import { darkenColor } from "../utils";

dayjs.extend(relativeTime);
dayjs.extend(isToday);
dayjs.extend(isYesterday);

interface ConversationItemProps {
  conversation: {
    conversation_id: string;
    name: string;
    timings: string;
    propertyName: string;
    unread_messages: number;
    pending_approvals: number;
    avatarColor: string;
    latest_activity: string;
    is_reply_needed: boolean;
    status: string;
  };
  selected: boolean;
  onSelect: () => void;
}

const formatUpdatedTime = (updatedAt: string): string => {
  const date = dayjs(updatedAt);
  if (date.isToday()) return date.format("hh:mm A");
  if (date.isYesterday()) return "Yesterday";
  return date.format("DD/MM");
};

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  selected,
  onSelect,
}) => {
  return (
    <ListItem
      disablePadding
      sx={{
        backgroundColor: selected ? "#E3F2FD" : "#F8F9FA",
        borderRadius: "12px",
        marginBottom: "8px",
        transition: "background-color 0.3s ease-in-out, transform 0.2s ease",
        boxShadow: selected ? "0 4px 8px rgba(0, 0, 255, 0.1)" : "none",
        "&:hover": {
          backgroundColor: selected ? "#BBDEFB" : "#EDEDED",
          transform: "scale(1.02)",
          boxShadow: "0 2px 6px rgba(0, 0, 0, 0.1)",
        },
      }}
    >
      <ListItemButton
        onClick={onSelect}
        sx={{
          padding: "14px",
          display: "flex",
          gap: "14px",
          alignItems: "center",
          borderRadius: "12px",
        }}
      >
        {/* Avatar */}
        <Avatar
          sx={{
            bgcolor: selected
              ? darkenColor(conversation.avatarColor)
              : conversation.avatarColor,
            color: "white",
            width: "42px",
            height: "42px",
            fontSize: "1rem",
            fontWeight: "bold",
            transition: "background-color 0.2s ease-in-out",
          }}
        >
          {conversation.name.charAt(0)}
        </Avatar>

        {/* Text Section */}
        <Box flex={1}>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography
              fontWeight={conversation.is_reply_needed ? "bold" : 500}
              // color={conversation.unread_messages ? "#0D47A1" : "#333"}
              fontSize="1rem"
            >
              {conversation.name}
            </Typography>

            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ fontSize: "0.75rem" }}
            >
              {formatUpdatedTime(conversation.latest_activity)}
            </Typography>
          </Box>

          <Typography
            variant="body2"
            sx={{
              color: "#757575",
              fontSize: "0.85rem",
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "250px",
              marginTop: "2px",
            }}
          >
            {`${conversation.timings} | ${conversation.propertyName}`}
          </Typography>

          <Box mt={0.5} display="flex" gap={1}>
            <Tag
              style={{
                backgroundColor: "#fff",
                border: "1px solid #d9d9d9",
                fontSize: 12,
                padding: "2px 8px",
              }}
            >
              {conversation.status}
            </Tag>

            {conversation.is_reply_needed && (
              <Tag
                style={{
                  backgroundColor: "#fff",
                  border: "1px solid #d9d9d9",
                  fontSize: 12,
                  padding: "2px 8px",
                }}
              >
                Reply Needed
              </Tag>
            )}
          </Box>
        </Box>
      </ListItemButton>
    </ListItem>
  );
};

export default ConversationItem;
