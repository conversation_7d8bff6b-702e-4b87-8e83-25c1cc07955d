import React, { useCallback, useEffect, useState } from "react";
import { Switch, Input, Skeleton } from "antd";
import {
  getListingDetails,
  updateListingDetailsAPI,
} from "../../../../services/targetAPI";

export default function Policies({
  selectedListing,
}: {
  selectedListing: string;
}) {
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [policies, setPolicies] = useState<any | null>(null);
  const [editedDetails, setEditedDetails] = useState<any | null>(null);

  const loadListingDetails = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getListingDetails(selectedListing, "policies");
      setPolicies(res);
      setEditedDetails({
        cancellationPolicy: res.userPolicies.cancellationPolicy || "",
        refundPolicy: res.userPolicies.refundPolicy || "",
        smokingAllowed: res.houseRules.smokingAllowed || false,
        username: res.wifi.username || "",
        password: res.wifi.password || "",
      });
    } catch (error) {
      console.error("Failed to fetch policies:", error);
    } finally {
      setLoading(false);
    }
  }, [selectedListing]);

  useEffect(() => {
    selectedListing && loadListingDetails();
  }, [selectedListing]);

  const handleSave = async () => {
    try {
      await updateListingDetailsAPI(selectedListing, {
        cancellation_policy: editedDetails.cancellationPolicy,
        refund_policy: editedDetails.refundPolicy,
        smoking_allowed: editedDetails.smokingAllowed,
        username: editedDetails.username,
        password: editedDetails.password,
      });
      await loadListingDetails();
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to save policy updates:", error);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    if (policies) {
      setEditedDetails({
        cancellationPolicy: policies.userPolicies.cancellationPolicy || "",
        refundPolicy: policies.userPolicies.refundPolicy || "",
        smokingAllowed: policies.houseRules.smokingAllowed || false,
        username: policies.wifi.username || "",
        password: policies.wifi.password || "",
      });
    }
  };

  const handleChange = (field: string, value: any) => {
    setEditedDetails((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="space-y-10 text-gray-800 text-sm">
      {loading ? (
        <div className="space-y-6">
          <Skeleton active paragraph={{ rows: 3 }} />
          <Skeleton active paragraph={{ rows: 2 }} />
        </div>
      ) : (
        policies && (
          <>
            {/* House Rules */}
            <section>
              <div className="flex justify-between items-center mb-3">
                <h2 className="text-lg font-semibold">House Rules</h2>
                {!isEditing && (
                  <button
                    className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors"
                    onClick={() => setIsEditing(true)}
                  >
                    Edit
                  </button>
                )}
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-2 gap-x-8">
                <p>
                  <span className="font-medium">Check-in:</span>{" "}
                  {policies.houseRules.checkIn || ""}
                </p>
                <p>
                  <span className="font-medium">Check-out:</span>{" "}
                  {policies.houseRules.checkOut || ""}
                </p>
                <p>
                  <span className="font-medium">Pet Allowed:</span>{" "}
                  {policies.houseRules.petAllowed ? "Yes" : "No"}
                </p>
                <p className="flex items-center gap-2">
                  <span className="font-medium">Smoking Allowed:</span>{" "}
                  {isEditing ? (
                    <Switch
                      checked={editedDetails.smokingAllowed}
                      onChange={(val) => handleChange("smokingAllowed", val)}
                    />
                  ) : policies.houseRules.smokingAllowed ? (
                    "Yes"
                  ) : (
                    "No"
                  )}
                </p>
              </div>
            </section>

            {/* User-defined Policies */}
            <section>
              <div className="flex justify-between items-center mb-3">
                <h2 className="text-lg font-semibold">Policies</h2>
              </div>
              <div className="space-y-4 text-gray-700">
                <div>
                  <p className="font-medium mb-1">Cancellation Policy</p>
                  {isEditing ? (
                    <Input.TextArea
                      rows={3}
                      value={editedDetails.cancellationPolicy}
                      onChange={(e) =>
                        handleChange("cancellationPolicy", e.target.value)
                      }
                    />
                  ) : (
                    <p>{policies.userPolicies.cancellationPolicy || ""}</p>
                  )}
                </div>
                <div>
                  <p className="font-medium mb-1">Refund Policy</p>
                  {isEditing ? (
                    <Input.TextArea
                      rows={3}
                      value={editedDetails.refundPolicy}
                      onChange={(e) =>
                        handleChange("refundPolicy", e.target.value)
                      }
                    />
                  ) : (
                    <p>{policies.userPolicies.refundPolicy || ""}</p>
                  )}
                </div>
              </div>
            </section>

            <section>
              <div className="flex justify-between items-center mb-3">
                <h2 className="text-lg font-semibold">WiFi</h2>
              </div>
              <div className="space-y-4 text-gray-700">
                <div>
                  <p className="font-medium mb-1">Username</p>
                  {isEditing ? (
                    <Input.TextArea
                      rows={2}
                      value={editedDetails.username}
                      onChange={(e) => handleChange("username", e.target.value)}
                    />
                  ) : (
                    <p>{policies.wifi.username || ""}</p>
                  )}
                </div>
                <div>
                  <p className="font-medium mb-1">Password</p>
                  {isEditing ? (
                    <Input.Password
                      value={editedDetails.password}
                      onChange={(e) => handleChange("password", e.target.value)}
                    />
                  ) : (
                    <p>{policies.wifi.password || ""}</p>
                  )}
                </div>
              </div>
            </section>

            {isEditing && (
              <div className="flex gap-4 pt-4">
                <button
                  onClick={handleSave}
                  className="text-sm px-4 py-1 rounded bg-blue-600 text-white hover:bg-blue-700 transition"
                >
                  Save
                </button>
                <button
                  onClick={handleCancel}
                  className="px-4 py-1 border rounded text-gray-700"
                >
                  Cancel
                </button>
              </div>
            )}
          </>
        )
      )}
    </div>
  );
}
