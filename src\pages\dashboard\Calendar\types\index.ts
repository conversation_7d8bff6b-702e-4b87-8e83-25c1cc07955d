export interface Listing {
  uid: string;
  name: string;
}

export interface Booking {
  listingUID: string;
  date: string;
  channel: string;
  guestName: string;
  amount: number;
  arrival_date?: string;
  departure_date?: string;
}

export interface Block {
  listingUID?: string;
  start_date?: string;
  end_date?: string;
  notes?: string;
  type?: 'blocked' | 'owner_stay' | 'maintenance';
}

export interface Rate {
  rate: number;
  minNights?: number;
}

export interface Rates {
  [listingUID: string]: {
    [date: string]: Rate;
  };
}