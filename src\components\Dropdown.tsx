import React from "react";
import { Select, Tag } from "antd";
import "tailwindcss/tailwind.css";
import CustomLightTooltip from "./CustomLightTooltip";

interface TreeDropdownProps {
  countries: Record<string, string[]>;
  selectedItems: string[];
  displayItems: string[];
  setSelectedItems: React.Dispatch<React.SetStateAction<string[]>>;
  setDisplayItems: React.Dispatch<React.SetStateAction<string[]>>;
}

const CheckboxTreeDropdown: React.FC<TreeDropdownProps> = ({
  countries,
  selectedItems,
  setSelectedItems,
  setDisplayItems,
}) => {
  // Handle removing items when tags are closed
  const handleTagClose = (item: string) => {
    const isCountry = countries.hasOwnProperty(item);
    const states = isCountry ? countries[item] : [];

    setSelectedItems((prevSelected) =>
      prevSelected.filter(
        (selected) => selected !== item && !states.includes(selected)
      )
    );
    setDisplayItems((prevDisplay) => prevDisplay.filter((d) => d !== item));
  };

  // Handle selection change
  const handleSelectionChange = (values: string[]) => {
    const newSelectedItems = [...values];

    // Remove states if their parent country is selected
    values.forEach((value) => {
      if (countries.hasOwnProperty(value)) {
        const countryStates = countries[value];
        countryStates.forEach((state) => {
          // Remove the state from selectedItems
          const stateIndex = newSelectedItems.indexOf(state);
          if (stateIndex > -1) {
            newSelectedItems.splice(stateIndex, 1); // Remove the state from selectedItems
          }
        });
      }
    });

    setSelectedItems(newSelectedItems);
    setDisplayItems(newSelectedItems); // Update displayItems with cleaned states
  };

  // Handle when a country is selected, ensuring all states are also selected
  const handleCountrySelection = (values: string[]) => {
    const newSelectedItems: string[] = [];

    values.forEach((value) => {
      newSelectedItems.push(value); // Add the selected country/state
      if (countries.hasOwnProperty(value)) {
        const countryStates = countries[value];
        // Ensure all states of the country are added to selectedItems
        countryStates.forEach((state) => {
          if (!newSelectedItems.includes(state)) {
            newSelectedItems.push(state);
          }
        });
      }
    });

    // If a country is deselected, remove its states from selectedItems
    const removedCountries = selectedItems.filter(
      (item) => !values.includes(item)
    );
    removedCountries.forEach((country) => {
      if (countries.hasOwnProperty(country)) {
        const countryStates = countries[country];
        countryStates.forEach((state) => {
          const stateIndex = newSelectedItems.indexOf(state);
          if (stateIndex > -1) {
            newSelectedItems.splice(stateIndex, 1); // Remove the state from selectedItems
          }
        });
      }
    });

    setSelectedItems(newSelectedItems);
    // Update displayItems with newSelectedItems, removing individual states if country is selected
    const displayItems = newSelectedItems.filter((item) => {
      const isCountry = countries.hasOwnProperty(item);
      return isCountry || !newSelectedItems.includes(
        Object.keys(countries).find((country) =>
          countries[country].includes(item)
        ) || ""
      );
    });
    setDisplayItems(displayItems);
  };

  // Custom tag rendering for selected items
  const tagRender = (props: any) => {
    const { label, value, closable, onClose } = props;
    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };

    let tagColor = "cyan";
    if (value === "airbnbOfficial") {
      tagColor = "volcano";
    } else if (value === "bookingcom") {
      tagColor = "geekblue";
    } else if (value === "ownerStay") {
      tagColor = "lime";
    }

    return (
      <CustomLightTooltip title={label}>
        <Tag
          color={tagColor}
          onMouseDown={onPreventMouseDown}
          closable={closable}
          onClose={onClose}
          style={{ marginInlineEnd: 4 }}
        >
          {label?.toString()?.toUpperCase()}
        </Tag>
      </CustomLightTooltip>
    );
  };

  return (
    <div className="w-full h-full">
      <Select
        mode="multiple"
        allowClear
        value={selectedItems}
        onChange={(values) => handleCountrySelection(values)}
        tagRender={tagRender}
        placeholder="Select locations"
        style={{ width: "100%", height: "100%" }}
        dropdownStyle={{ maxHeight: 300, overflowY: "auto" }}
        maxTagCount="responsive"
        maxTagPlaceholder={(omittedValues) => `+${omittedValues.length} more`}
      >
        {Object.entries(countries).map(([country, states]) => (
          <Select.OptGroup key={country} label={country}>
            <Select.Option key={country} value={country}>
              {country} (All)
            </Select.Option>
            {states.map((state) => (
              <Select.Option key={state} value={state} disabled={selectedItems.includes(country)}>
                {state}
              </Select.Option>
            ))}
          </Select.OptGroup>
        ))}
      </Select>
    </div>
  );
};

export default CheckboxTreeDropdown;
