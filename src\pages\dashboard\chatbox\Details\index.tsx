import { useEffect, useState } from "react";
import GuestInfoCard from "./GuestInfoCard";
import BookingDetailsCard from "./BookingDetailsCard";
import QuoteDetailsCard from "./QuoteDetailsCard";
import { Typography } from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../../../redux/store";
import {
  addMessage,
  approveMessage,
  setMessages,
  setProperty,
  setReservation,
} from "../../../../redux/chatSlice";
import { fetchDetailsByID } from "../../../../services/chatAPI";

const DetailsTab = () => {
  const dispatch = useDispatch();
  const selectedConversation = useSelector(
    (state: RootState) => state.chat.selectedConversation
  );

  const propertyDetails = useSelector(
    (state: RootState) => state.chat.properties
  );

  const reservationsDetails = useSelector(
    (state: RootState) => state.chat.reservations
  );
  const reservationDetails = selectedConversation
    ? reservationsDetails[selectedConversation]
    : null;

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchDetails = async () => {
    if (!selectedConversation) return;

    try {
      setIsLoading(true);
      const response = await fetchDetailsByID(selectedConversation);
      if (response.data.reservation) {
        dispatch(
          setReservation({
            conversationId: selectedConversation,
            reservation: response.data.reservation, // Now in the required format
          })
        );
      }

      if (response.data.property) {
        dispatch(
          setProperty({
            conversationId: selectedConversation,
            property: response.data.property,
          })
        );
      }
    } catch (err) {
      console.error("Failed to fetch messages", err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDetails();
  }, [selectedConversation]);

  return (
    <>
      {reservationDetails ? (
        <div className="flex flex-col h-full w-[320px] max-w-[320px] border-l bg-white">
          {/* Header - No scroll */}
          <div className="flex h-16 items-center justify-between border-b px-6 py-6 shrink-0">
            <Typography variant="h5" fontWeight="bold">
              Details
            </Typography>
          </div>
          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            <GuestInfoCard reservationDetails={reservationDetails} />
            <BookingDetailsCard reservationDetails={reservationDetails} />
            <QuoteDetailsCard reservationDetails={reservationDetails} />
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default DetailsTab;
