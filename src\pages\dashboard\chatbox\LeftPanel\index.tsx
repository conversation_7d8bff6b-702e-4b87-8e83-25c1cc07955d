import { useDispatch, useSelector } from "react-redux";
import { useState, useEffect, useRef } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { RootState } from "../../../../redux/store";
import {
  selectConversation,
  setConversations,
  addConversations,
  upsertConversation,
  resetChat,
} from "../../../../redux/chatSlice";
import { List, Typography, Box, Skeleton, Avatar } from "@mui/material";
import SettingsIcon from "@mui/icons-material/Settings";
import ConversationItem from "./ConversationItem";
import { fetchConversations } from "../../../../services/chatAPI";
import { getRandomColor } from "../utils";
import ExpandableSearch from "./SearchBar"; // adjust path as needed
import InboxIcon from "@mui/icons-material/Inbox";
import ConversationFilters from "./ConversationFilters";
import MenuIcon from "@mui/icons-material/Menu"; // Add this import
import ChromeReaderModeOutlinedIcon from '@mui/icons-material/ChromeReaderModeOutlined';

const NAVBAR_HEIGHT = 60;
const token = localStorage.getItem("accessToken");
const BACKENDBASEURL = process.env.REACT_APP_SOCKET_BASE_URL;

const WEBSOCKET_URL = `${
  window.location.protocol === "https:" ? "wss" : "ws"
}://${BACKENDBASEURL}/ws/conversation/?token=${token}`;

const PAGE_SIZE = 20;

const ConversationList = () => {
  const dispatch = useDispatch();
  const conversations = useSelector(
    (state: RootState) => state.chat.conversations
  );
  const selectedConversation = useSelector(
    (state: RootState) => state.chat.selectedConversation
  );

  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const loadingRef = useRef(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<string[]>([]);

  const loadConversations = async (
    pageNum: number,
    search = "",
    types: string[] = []
  ) => {
    if (loadingRef.current) return;
    loadingRef.current = true;

    try {
      setIsLoading(true)
      const response = await fetchConversations(
        pageNum,
        PAGE_SIZE,
        search,
        types
      );

      if (!response.data.next) {
        setHasMore(false);
      }

      if (pageNum === 1) {
        dispatch(setConversations(response.data.results));
      } else {
        dispatch(addConversations(response.data.results));
      }

      setPage((prevPage) => prevPage + 1);
    } catch (err) {
      console.error("Failed to fetch conversations", err);
    } finally {
      loadingRef.current = false;
      setIsLoading(false)
    }
  };

  useEffect(() => {
    setPage(1);
    setHasMore(true);
    dispatch(resetChat()); // ✅ Reset conversations/messages/selection
    loadConversations(1, searchQuery, filters);
  }, [searchQuery, filters]);

  useEffect(() => {
    const socket = new WebSocket(WEBSOCKET_URL);

    socket.onopen = () => {
      console.log("WebSocket connected");
    };

    socket.onmessage = (event) => {
      const data = JSON.parse(event.data);

      if (data.event.includes("_updated") && data.data) {
        dispatch(upsertConversation(data.data));
      }
    };

    socket.onclose = () => {
      console.log("WebSocket disconnected");
    };

    return () => {
      socket.close();
    };
  }, []);

  return (
    <div
      className="w-1/4 h-screen flex flex-col bg-white overflow-hidden"
      style={{ borderRight: "1px solid #e0e0e0", position: "relative" }}
    >
      <div className="flex h-16 items-center justify-between px-4 py-2 border-b">
        <div className="flex items-center gap-2">
          <ChromeReaderModeOutlinedIcon fontSize="medium" />
          
          <Typography variant="h5" fontWeight="bold">
            Inbox
          </Typography>
        </div>

        <ExpandableSearch onSearch={setSearchQuery} isLoading={isLoading} />
      </div>
      <div className="px-4 py-2">
        <ConversationFilters
          selectedFilters={filters}
          onFilterChange={(updatedFilters) => {
            setFilters(updatedFilters); // useEffect will handle fetching
          }}
          isLoading={isLoading}
        />
      </div>

      {/* Scrollable Conversation List */}
      <div
        id="conversation-scroll"
        className="flex-1 overflow-y-auto custom-scrollbar"
        style={{ flex: 1 }}
      >
        <InfiniteScroll
          dataLength={conversations.length}
          next={() => loadConversations(page, searchQuery, filters)}
          hasMore={hasMore}
          loader={
            <Box sx={{ display: "flex", flexDirection: "column", p: 2 }}>
              <Skeleton variant="text" width="100%" height={20} />
              <Skeleton variant="text" width="90%" height={20} />
            </Box>
          }
          scrollableTarget="conversation-scroll"
        >
          <List sx={{ padding: "8px" }}>
            {conversations.map((conv) => (
              <ConversationItem
                key={conv?.conversation_id}
                conversation={{
                  ...conv,
                  avatarColor: getRandomColor(conv.name),
                }}
                selected={selectedConversation === conv?.conversation_id}
                onSelect={() =>
                  dispatch(selectConversation(conv?.conversation_id))
                }
              />
            ))}
          </List>
        </InfiniteScroll>

        {!loadingRef.current && conversations.length === 0 && (
          <Typography align="center" color="textSecondary" sx={{ py: 4 }}>
            No conversations found
          </Typography>
        )}
      </div>

      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 2,
          padding: "16px",
          borderTop: "1px solid #e0e0e0",
          backgroundColor: "#F9FAFB",
          cursor: "pointer",
          transition: "background 0.3s",
          "&:hover": { backgroundColor: "#f0f0f0" },
          position: "sticky",
          bottom: 0,
          width: "100%",
          zIndex: 10,
        }}
        onClick={() => console.log("Open Settings")}
      >
        <Avatar sx={{ bgcolor: "#1976D2", width: 40, height: 40 }}>
          <SettingsIcon sx={{ color: "#FFF" }} />
        </Avatar>
        <Typography variant="body1" fontWeight="bold" color="textPrimary">
          Settings & Preferences
        </Typography>
      </Box>

      {/* Sticky Hamburger Icon (Bottom-Right) */}
      <Box
        sx={{
          position: "absolute",
          bottom: 16,
          right: 16,
          zIndex: 20,
          backgroundColor: "#FFF",
          border: "1px solid #e0e0e0",
          borderRadius: "50%",
          width: "40px",
          height: "40px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          cursor: "pointer",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          "&:hover": {
            backgroundColor: "#f5f5f5",
          },
        }}
        onClick={() => console.log("Hamburger menu clicked")}
      >
        <MenuIcon fontSize="small" />
      </Box>
    </div>
  );
};

export default ConversationList;
