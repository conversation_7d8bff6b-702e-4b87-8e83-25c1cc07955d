import React from "react";
import PageHeader from "../../../components/PageHeader";

interface CalendarHeaderProps {
  selectedMonth: number;
  selectedYear: number;
  listingsCount: number;
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({
  selectedMonth,
  selectedYear,
  listingsCount,
}) => {
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const years = [2023, 2024, 2025, 2026];

  const getMonthName = (month: number): string => {
    return months[month];
  };

  return (
    <div className="mb-6">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* <h1 className="text-2xl font-bold px-2 py-2 pt-4">Calendar</h1> */}
          <div className="px-4 py-2 pt-4">
          <PageHeader text="Calendar" />
          </div>
        </div>
      </div>


    </div>
  );
};

export default CalendarHeader;
