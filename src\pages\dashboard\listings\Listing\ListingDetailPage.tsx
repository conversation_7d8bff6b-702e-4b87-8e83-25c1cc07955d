import React, { useCallback, useEffect, useState } from "react";
import { Tabs } from "antd";
import ListingDetailsTab from "./ListingDetailsTab";
import Sidebar from "../Sidebar";
import RatesAvailabilitySection from "./RatesAvailabilitySection";
import PoliciesSection from "./PoliciesSection";

const { TabPane } = Tabs;

export default function ListingPage({
  selectedListing,
  selectedListingName,
}: {
  selectedListing: string;
  selectedListingName: string;
}) {
  const [activeTab, setActiveTab] = useState("details");

  return (
    <div className="">
      <h1 className="text-2xl font-semibold mb-4">{selectedListingName}</h1>

      <Tabs activeKey={activeTab} onChange={setActiveTab} className="mb-6">
        <TabPane tab="Listing details" key="details">
          {activeTab === "details" && (
            <ListingDetailsTab selectedListing={selectedListing} />
          )}
        </TabPane>

        <TabPane tab="Price & Fees" key="pricing">
          {activeTab === "pricing" && (
            <RatesAvailabilitySection selectedListing={selectedListing} />
          )}
        </TabPane>
        <TabPane tab="Additional info" key="policies">
          {activeTab === "policies" && (
            <PoliciesSection selectedListing={selectedListing} />
          )}
        </TabPane>
      </Tabs>
    </div>
  );
}
