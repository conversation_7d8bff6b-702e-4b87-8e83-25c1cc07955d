import { Avatar } from "@mui/material";
import { getRandomColor } from "../utils";

const GuestInfoCard = ({ reservationDetails }: any) => {
  
  return (
    <div className="bg-white shadow-sm rounded-lg p-4 space-y-4 break-all">
      {/* Property details */}
      <div className="grid grid-cols-2 gap-y-2 gap-x-4 text-sm">
        <div className="text-gray-500">Status</div>
        <div className="text-gray-800 font-medium">{reservationDetails?.status || ""}</div>

        <div className="text-gray-500">Listing Name</div>
        <div className="text-gray-800 font-medium">{reservationDetails?.listingName || ""}</div>

        <div className="text-gray-500">Channel</div>
        <div className="text-gray-800 font-medium">{reservationDetails?.channel || ""}</div>
      </div>

      {/* Avatar and contact info below */}
      <div className="flex gap-4 border-t pt-4">
        <Avatar
          sx={{
            bgcolor: getRandomColor(reservationDetails?.guestName || ""), // fallback or dynamically generate
            color: "white",
            width: "48px",
            height: "48px",
            fontSize: "1rem",
            fontWeight: "bold",
            transition: "background-color 0.2s ease-in-out",
          }}
        >
          {reservationDetails?.guestName?.charAt(0) || ""}
        </Avatar>

        <div className="flex flex-col justify-start w-full">
          <div className="font-semibold text-gray-800 mb-2 text-sm">
            {reservationDetails?.guestName || "N/A"}
          </div>
          <div className="grid grid-cols-2 text-sm text-gray-600">
            <div>Email</div>
            <div className="text-right">{reservationDetails?.guest_email || "—"}</div>

            <div>Phone</div>
            <div className="text-right">{reservationDetails?.phone || "—"}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GuestInfoCard;
