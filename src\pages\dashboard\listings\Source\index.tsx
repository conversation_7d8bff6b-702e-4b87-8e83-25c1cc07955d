import React, { useEffect, useState } from "react";
import { <PERSON>ton, <PERSON>dal, Dropdown, <PERSON>u, Skeleton } from "antd";
import {
  CheckCircleTwoTone,
  SyncOutlined,
  BookOutlined,
  FileTextOutlined,
  FileDoneOutlined,
  EllipsisOutlined,
  DeleteOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import ViewComfyOutlinedIcon from "@mui/icons-material/ViewComfyOutlined";
import NewSourceModal from "./NewSourceModal";
import {
  DeleteSourceAPI,
  fetchSourcesApi,
  syncTargetSheetsAPI
} from "../../../../services/targetAPI";
import {message} from "antd"
import { useDispatch, useSelector } from "react-redux";
import { triggerSync, fetchSyncStatus } from "../../../../redux/syncSlice";
import { AppDispatch, RootState } from "../../../../redux/store";

type SourceItem = {
  id: number;
  name: string;
  type: string;
};

type SourceData = {
  pms: {
    name: string;
    type: string;
    listings: number;
  };
  moreSources: SourceItem[];
};

const getSourceIcon = (name: string, type: string) => {
  if (type.toLowerCase().includes("google")) return <FileDoneOutlined />;
  if (type.toLowerCase() === "document") return <FileTextOutlined />;
  return <FileTextOutlined />;
};

export default function SourceSection() {
  const dispatch = useDispatch<AppDispatch>();
  const { isSyncing, lastSyncDateTime } = useSelector(
    (state: RootState) => state.sync
  );

  const [sourceData, setSourceData] = useState<SourceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [selectedSourceId, setSelectedSourceId] = useState<number | null>(null);
  const [isSyncModalOpen, setIsSyncModalOpen] = useState(false);

  const loadSources = async () => {
    try {
      const response = await fetchSourcesApi();
      setSourceData(response.data);
    } catch (error) {
      console.error("Load error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (sourceId: number) => {
    setSelectedSourceId(sourceId);
    setIsConfirmModalOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedSourceId) {
      try {
        await DeleteSourceAPI(selectedSourceId);
        setIsConfirmModalOpen(false);
        loadSources();
      } catch (error) {
        console.error("Delete error:", error);
      }
    }
  };

  const handleSyncConfirm = () => {
    dispatch(triggerSync());
    setIsSyncModalOpen(false);
  };

  const dropdownMenu = (sourceId: number) => (
    <Menu>
      <Menu.Item icon={<ReloadOutlined />} onClick={()=>handleResyncClick(sourceId)}>Re-sync</Menu.Item>
      <Menu.Item
        icon={<DeleteOutlined />}
        danger
        onClick={() => handleDeleteClick(sourceId)}
      >
        Remove this source
      </Menu.Item>
    </Menu>
  );
  const handleResyncClick = async (sourceId: number) => {
  try {
    setLoading(true);
    await syncTargetSheetsAPI();
    // Show success state or update UI
    console.log('Sync completed successfully');
  } catch (error) {
    // Show error state
    console.error('Sync failed:', error);
    message.error("Sync failed!")
  } finally {
    setLoading(false);
  }
};
  useEffect(() => {
    loadSources();
    dispatch(fetchSyncStatus());
    const interval = setInterval(() => dispatch(fetchSyncStatus()), 15000);
    return () => clearInterval(interval);
  }, [dispatch]);

  return (
    <div>
      {loading ? (
        <Skeleton active paragraph={{ rows: 4 }} />
      ) : (
        <>
          <div className="flex justify-between items-center mb-4">
            <div className="flex gap-4 items-center">
              <ViewComfyOutlinedIcon />
              <h1 className="text-xl font-semibold">Sources</h1>
            </div>
            <NewSourceModal loadSources={loadSources} />
          </div>

          {/* PMS Source */}
          {sourceData && (
            <div className="bg-white rounded-xl p-6 shadow mb-6">
              <div className="flex items-center gap-2 text-xl font-semibold mb-4">
                <BookOutlined />
                {sourceData.pms.type}
              </div>
              <div className="flex items-center justify-between border-t pt-4">
                <div className="flex items-center gap-2">
                  <CheckCircleTwoTone twoToneColor="#52c41a" />
                  <span className="font-medium">{sourceData.pms.name}</span>
                </div>
                <div className="text-gray-500 text-sm">
                  {sourceData.pms.listings} listings
                </div>
                <div className="flex flex-col items-end">
                  <Button
                    icon={<SyncOutlined spin={isSyncing} />}
                    loading={isSyncing}
                    onClick={() => setIsSyncModalOpen(true)}
                    disabled={isSyncing}
                  >
                    {isSyncing ? "Syncing..." : "Sync Now"}
                  </Button>
                  {lastSyncDateTime && (
                    <div className="text-gray-400 text-xs mt-1">
                      Last Sync:{" "}
                      {new Date(lastSyncDateTime).toLocaleString("en-GB")}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* More Sources */}
          {sourceData && (
            <div className="bg-white rounded-xl p-6 shadow">
              <div className="flex items-center gap-2 text-xl font-semibold mb-4">
                <BookOutlined />
                More sources
              </div>
              {sourceData.moreSources.map((item, idx) => (
                <div
                  key={idx}
                  className="flex items-center justify-between border-t pt-4 mt-2"
                >
                  <div className="flex items-center gap-2">
                    <CheckCircleTwoTone twoToneColor="#52c41a" />
                    {getSourceIcon(item.name, item.type)}
                    <span className="font-medium">{item.name}</span>
                  </div>
                  <div className="text-gray-500 text-sm">{item.type}</div>

                  <Dropdown overlay={dropdownMenu(item.id)} trigger={["click"]}>
                    <Button icon={<EllipsisOutlined />}>Manage</Button>
                  </Dropdown>
                </div>
              ))}
            </div>
          )}

          {/* Modals */}
          <Modal
            title="New Source"
            open={isModalOpen}
            onCancel={() => setIsModalOpen(false)}
            footer={null}
          >
            <p>Modal Content Here</p>
          </Modal>

          <Modal
            title="Confirm Deletion"
            open={isConfirmModalOpen}
            onOk={confirmDelete}
            onCancel={() => setIsConfirmModalOpen(false)}
            okText="Yes, delete"
            cancelText="Cancel"
          >
            <p>Are you sure you want to remove this source?</p>
          </Modal>

          <Modal
            title="Confirm Sync"
            open={isSyncModalOpen}
            onOk={handleSyncConfirm}
            onCancel={() => setIsSyncModalOpen(false)}
            okText="Confirm"
            cancelText="Cancel"
          >
            <p>
              Are you sure you want to start the sync process? This may take a
              while.
            </p>
          </Modal>
        </>
      )}
    </div>
  );
}
