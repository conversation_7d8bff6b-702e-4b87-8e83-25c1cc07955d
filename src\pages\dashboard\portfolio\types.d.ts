export interface Revenue {
    month: string;
    net_rev_amount: number;
    currency: string;
    net_comparison_val?: string;
    net_comparison_percent?: string;
  }
  
 export interface Property {
    id: number;
    unit: string;
    currency: string;
    revenues: Revenue[];
    total_net_revenue: number | string;
    total_comparison_percent: {
      net_comparison_percent: string;
    };
    total_comparison_value: {
      net_comparison_value: string;
    };
  }

  export interface ChannelData {
    channelName: string;
    [key: string]: string | number; // Month columns can have dynamic keys
  }