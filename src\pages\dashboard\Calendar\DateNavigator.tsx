import React from "react";
import { But<PERSON>, Select, Input, Spin } from "antd";
import {
  LeftOutlined,
  RightOutlined,
  SearchOutlined,
  LoadingOutlined,
} from "@ant-design/icons";

const { Option } = Select;

interface DateNavigatorProps {
  visibleDates: Date[];
  onPrevious: () => void;
  onNext: () => void;
  listingColumnWidth: number;
  onResizeStart: (e: React.MouseEvent) => void;
  selectedMonth: number;
  selectedYear: number;
  listingsCount: number;
  searchUID: string;
  onSearchChange: (value: string) => void;
  onMonthChange: (month: number) => void;
  onYearChange: (year: number) => void;
  onTodayClick: () => void;
  navigationLoading?: boolean;
}

const DateNavigator: React.FC<DateNavigatorProps> = ({
  visibleDates,
  onPrevious,
  onNext,
  listingColumnWidth,
  onResizeStart,
  selectedMonth,
  selectedYear,
  listingsCount,
  searchUID,
  onSearchChange,
  onMonth<PERSON>hange,
  onYearChange,
  onTodayClick,
  navigationLoading = false,
}) => {
  const getMonthName = (month: number): string => {
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    return months[month];
  };

  const getDayName = (date: Date): string => {
    return date.toLocaleDateString("en-US", { weekday: "short" });
  };

  const isCurrentDate = (date: Date): boolean => {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  return (
    <div>
      <div
        className="flex items-center bg-white"
        style={{
          minWidth: `${listingColumnWidth + visibleDates.length * 120}px`,
        }}
      >
        <div
          className="px-4 py-4 border-r border-gray-300 bg-white sticky left-0"
          style={{
            width: `${listingColumnWidth}px`,
            boxShadow: "1px 0 0px rgba(0, 0, 0, 0.1)",
            zIndex: 30,
          }}
        >
          <div className="flex items-center justify-center">
            <span className="text-sm font-bold text-gray-800">
              {listingsCount} Listings
            </span>
          </div>
        </div>

        <div className="flex-1 flex">
          {visibleDates.map((date, index) => {
            const isLast = index === visibleDates.length - 1;
            const isFirst = index === 0;

            return (
              <div
                key={`top-${index}`}
                className={`${isFirst ? "w-50" : "flex-1"} text-center py-2 bg-white border-b border-gray-200 ${isLast ? "border-r border-gray-300" : ""}`}
              >
                {isFirst && (
                  <div className="flex items-center justify-center gap-3 pl-4">
                    <div className="text-sm font-bold text-black">
                      <div className="flex gap-2 justify-center">
                      <Select
                      value={selectedMonth}
                      onChange={onMonthChange}
                      className="w-24"
                      size="small"
                    >
                      {Array.from({ length: 12 }, (_, i) => (
                        <Option key={i} value={i}>
                          {getMonthName(i)}
                        </Option>
                      ))}
                    </Select>
                    <Select
                      value={selectedYear}
                      onChange={onYearChange}
                      className="w-20"
                      size="small"
                    >
                      {Array.from({ length: 10 }, (_, i) => {
                        const year = new Date().getFullYear() - 2 + i;
                        return (
                          <Option key={year} value={year}>
                            {year}
                          </Option>
                        );
                      })}
                    </Select>
                    </div>
                    </div>
                    <Button
                      onClick={onTodayClick}
                      size="small"
                      className="bg-gray-300 text-black border-gray-300 hover:bg-gray-400 hover:border-gray-400 hover:text-black"
                    >
                      Today
                    </Button>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
          {/* Month-Year Row below control row */}
      <div
        className="flex items-center bg-white border-b border-gray-200"
        style={{
          minWidth: `${listingColumnWidth + visibleDates.length * 120}px`,
        }}
      >
        <div
          className="bg-white sticky left-0 border-r border-gray-300"
          style={{
            width: `${listingColumnWidth}px`,
            boxShadow: "1px 0 0px rgba(0, 0, 0, 0.1)",
            zIndex: 30,
          }}
        >
           <div className="py-3" /> 
        </div>

        <div className="flex-1 text-center">
          <div className="text-base font-semibold text-gray-800">
            {getMonthName(visibleDates[0].getMonth())} {visibleDates[0].getFullYear()}
          </div>
        </div>
      </div>
      {/* Original header row */}
      <div
        className="flex items-center border-b border-gray-300 bg-gray-50"
        style={{
          minWidth: `${listingColumnWidth + visibleDates.length * 120}px`,
        }}
      >
        <div
          className="px-4 py-4 border-r border-gray-300 group bg-white sticky left-0"
          style={{
            width: `${listingColumnWidth}px`,
            boxShadow: "1px 0 0px rgba(0, 0, 0, 0.1)",
            zIndex: 30,
          }}
        >
          <div className="flex items-center justify-center">
            <Input
              allowClear
              placeholder="Search listings..."
              prefix={<SearchOutlined />}
              value={searchUID}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full"
              size="large"
            />
          </div>
          {/* Resize handle */}
          <div
            className="absolute right-0 top-0 bottom-0 w-2 cursor-col-resize hover:bg-blue-200 opacity-0 group-hover:opacity-100 transition-opacity"
            onMouseDown={onResizeStart}
            title="Drag to resize column"
          />
        </div>

        <div className="flex-1 flex">
          {visibleDates.map((date, index) => {
            const isFirst = index === 0;
            const isLast = index === visibleDates.length - 1;
            const isToday = isCurrentDate(date);

            return (
              <div
                key={index}
                className="flex-1 text-center py-2 border-r border-gray-300 last:border-r-0 relative"
                style={isToday ? { backgroundColor: "#41645c" } : {}}
              >
                {/* Navigation arrow for first date */}
                {isFirst && (
                  <Button
                    type="text"
                    icon={
                      navigationLoading ? (
                        <LoadingOutlined spin />
                      ) : (
                        <LeftOutlined />
                      )
                    }
                    onClick={onPrevious}
                    size="small"
                    title="Previous week"
                    disabled={navigationLoading}
                    className="absolute left-1 top-1/2 transform -translate-y-1/2 z-10 hover:bg-blue-100 rounded-full w-6 h-6 flex items-center justify-center"
                  />
                )}

                {/* Navigation arrow for last date */}
                {isLast && (
                  <Button
                    type="text"
                    icon={
                      navigationLoading ? (
                        <LoadingOutlined spin />
                      ) : (
                        <RightOutlined />
                      )
                    }
                    onClick={onNext}
                    size="small"
                    title="Next week"
                    disabled={navigationLoading}
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 z-10 hover:bg-blue-100 rounded-full w-6 h-6 flex items-center justify-center"
                  />
                )}

                <div
                  className={`flex flex-col sm:flex-row items-center justify-center gap-x-1 text-[11px] sm:text-xs font-medium ${
                    isToday ? "text-white" : "text-gray-800"
                  }`}
                >
                  <span>{getDayName(date)}</span>
                  <span>{date.getDate()}</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DateNavigator;
