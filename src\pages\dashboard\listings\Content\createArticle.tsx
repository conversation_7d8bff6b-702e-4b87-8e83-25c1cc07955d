import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import ReactQuill from "react-quill";
import { Input, Select, Button, message, Divider, Spin } from "antd";
import { SaveOutlined, CloseOutlined } from "@ant-design/icons";
import "react-quill/dist/quill.snow.css";
import "./custom-quill.css";
import { createArticle, fetchFoldersApi,fetchArticleById, 
  updateArticle  } from "../../../../services/targetAPI";

const { Option } = Select;
interface Folder {
  id: string;
  name: string;
}

const toolbarOptions = [
  [{ header: [1, 2, 3, false] }],
  ["bold", "italic", "underline", "strike"],
  [{ list: "ordered" }, { list: "bullet" }],
  [{ indent: "-1" }, { indent: "+1" }],
  [{ align: [] }],
  ["link", "clean"],
];

const ArticleEditor: React.FC = () => {
  const { articleId } = useParams(); // ← Get articleId from URL
  const [loading, setLoading] = useState(!!articleId);
  const [foldersLoading, setFoldersLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [content, setContent] = useState("");
  const [title, setTitle] = useState("");
  const [folder, setFolder] = useState<string | undefined>(undefined);
  const [contentType, setContentType] = useState("internal_article");
  const [folders, setFolders] = useState<Folder[]>([]);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchFolders = async () => {
      try {
        setFoldersLoading(true);
        const response = await fetchFoldersApi();
        setFolders(response.data || response);
      } catch (error) {
        console.error("Error fetching folders:", error);
        message.error("Failed to load folders");
      } finally {
        setFoldersLoading(false);
      }
    };

    fetchFolders();
  }, []);

    // Fetch article data for editing
  useEffect(() => {
    const fetchArticleData = async () => {
      if (!articleId) return;

      try {
        setLoading(true);
        const response = await fetchArticleById(articleId);
        
        setTitle(response.data.title || "");
        setFolder(response.data.folder || undefined);
        setContentType(response.data.content_type || "internal_article");
        setContent(response.data.content_sanitized || response.data.content_raw || "");
      } catch (error) {
        console.error("Error fetching article:", error);
        message.error("Failed to load article");
        navigate(-1); // Go back if article can't be loaded
      } finally {
        setLoading(false);
      }
    };

    fetchArticleData();
  }, [articleId, navigate]);

  const handleSave = async () => {
    if (!title.trim()) {
      message.error("Name is required");
      return;
    }
    if (saving) return;
    setSaving(true);
    const payload = {
      title,
      content_type: contentType,
      content_raw: content,
      ...(folder && { folder }), 
    };

    try {
      if (articleId) {
        await updateArticle(articleId, payload);
      } else {
        await createArticle(payload);
      }
      navigate(-1); // Go back to previous page
    } catch (error) {
      console.error("Error saving article:", error);
      message.error(
        articleId ? "Failed to update article" : "Failed to create article"
      );
    } finally {
      setSaving(false);
    }
  };
  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div
      className="bg-gray-100 p-4 sm:p-8"
      style={{ height: "calc(100vh - 65px)" }}
    >
      <div className="max-w-8xl mx-auto bg-white rounded-2xl shadow-xl px-6 sm:px-10 py-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-3xl font-semibold text-gray-800">
            {articleId ? "Edit Article" : "Create New Article"}
          </h1>
          <div className="space-x-4">
            <Button
              size="middle"
              className="bg-gray-700"
              onClick={() => setContent("")}
            >
              Clear
            </Button>
            <Button
              icon={<SaveOutlined />}
              type="primary"
              className="!bg-black !text-white"
              size="middle"
              onClick={handleSave}
              disabled={!title}
            >
              {saving ? "Saving..." : "Save"}
            </Button>
            <Button
              icon={<CloseOutlined />}
              size="middle"
              danger
              ghost
              onClick={() => navigate(-1)}
            />
          </div>
        </div>

        <Divider className="m-0 mb-6" />

        <div className="flex flex-col lg:flex-row gap-8">
          <div className="flex-1">
            <div className="h-[500px] border border-gray-200 rounded-lg overflow-hidden">
              <ReactQuill
                theme="snow"
                value={content}
                onChange={setContent}
                modules={{ toolbar: toolbarOptions }}
                placeholder="Start writing your article..."
                style={{ height: "100%", border: "none" }}
              />
            </div>
          </div>

          <div className="w-full lg:w-80 bg-gray-50 border border-gray-200 rounded-lg p-6 space-y-5 shadow-sm">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Article Name
              </label>
              <Input
                placeholder="Enter article title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                size="large"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Folder
              </label>
              <Select
                value={folder}
                onChange={setFolder}
                size="large"
                className="w-full"
                placeholder="Select a folder"
                loading={foldersLoading}
                allowClear
              >
                {folders.map((folderItem) => (
                  <Option key={folderItem.id} value={folderItem.id}>
                    {folderItem.name}
                  </Option>
                ))}
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Content Type
              </label>
              <Select
                value={contentType}
                onChange={setContentType}
                size="large"
                className="w-full"
              >
                <Option value="internal_article">Internal article</Option>
                <Option value="public_article">Public article</Option>
              </Select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleEditor;
