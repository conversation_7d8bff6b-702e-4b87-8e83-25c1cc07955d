export const getRandomColor = (name: string) => {
  const colors = [
    "#FF5733",
    "#3498DB",
    "#1ABC9C",
    "#9B59B6",
    "#E74C3C",
    "#2ECC71",
    "#F39C12",
  ];
  const safeName = name || "Anonymous";
  let hash = 0;
  for (let i = 0; i < safeName.length; i++) {
    hash = safeName.charCodeAt(i) + ((hash << 5) - hash);
  }
  return colors[Math.abs(hash % colors.length)];
};

export const darkenColor = (color: string, percent: number = 20) => {
  const num = parseInt(color.slice(1), 16);
  const amt = Math.round(2.55 * percent);
  const r = (num >> 16) - amt;
  const g = ((num >> 8) & 0x00ff) - amt;
  const b = (num & 0x0000ff) - amt;
  return `#${(
    0x1000000 +
    (r < 0 ? 0 : r) * 0x10000 +
    (g < 0 ? 0 : g) * 0x100 +
    (b < 0 ? 0 : b)
  )
    .toString(16)
    .slice(1)}`;
};
