import { Paper, Typography, Divider, Box } from "@mui/material";
import EventIcon from "@mui/icons-material/Event";
import PersonIcon from "@mui/icons-material/Person";
import InfoIcon from "@mui/icons-material/Info";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";

const ReservationPanel = () => {
  const selectedConversation = useSelector(
    (state: RootState) => state.chat.selectedConversation
  );
  const reservations = useSelector(
    (state: RootState) => state.chat.reservations
  );

  // Find the selected guest's reservation details
  const reservation = selectedConversation
    ? reservations[selectedConversation]
    : null;

  if (!reservation) return null; // Do not render if no guest is selected

  return (
    <div>
      <div className="flex h-16 items-center justify-between border-b">
        <div className="flex px-4">
          <Typography variant="h5" fontWeight="bold">
            Details
          </Typography>
        </div>
      </div>

      {/* Guest Name */}
      <Box display="flex" alignItems="center" gap={1}>
        <PersonIcon color="action" />
        <Typography variant="body1" fontWeight="bold">
          {reservation.guestName}
        </Typography>
      </Box>

      {/* Check-in Date */}
      <Box display="flex" alignItems="center" gap={1}>
        <EventIcon color="action" />
        <Typography variant="body2">
          <strong>Check-in:</strong> {reservation.checkIn}
        </Typography>
      </Box>

      {/* Check-out Date */}
      <Box display="flex" alignItems="center" gap={1}>
        <EventIcon color="action" />
        <Typography variant="body2">
          <strong>Check-out:</strong> {reservation.checkOut}
        </Typography>
      </Box>

      {/* Reservation Status */}
      <Box display="flex" alignItems="center" gap={1}>
        <InfoIcon color="action" />
        <Typography variant="body2">
          <strong>Status:</strong> {reservation.status}
        </Typography>
      </Box>
    </div>
  );
};

export default ReservationPanel;
