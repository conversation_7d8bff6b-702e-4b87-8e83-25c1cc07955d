import React, { useState, useEffect, useCallback } from "react";
import { Table, Input, Spin } from "antd";
import { useNavigate } from "react-router-dom";
import ListingDetailPage from "./ListingDetailPage";
import ViewComfyOutlinedIcon from "@mui/icons-material/ViewComfyOutlined";
import { fetchListings } from "../../../../services/targetAPI";

const { Search } = Input;

export default function ListingPage() {
  const [searchText, setSearchText] = useState("");
  const [listings, setListings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [initialLoad, setInitialLoad] = useState(true);
  const [selectedListing, setSelectedListing] = useState<string | undefined>();
  const [selectedListingName, setSelectedListingName] = useState<
    string | undefined
  >();

  const loadListings = useCallback(
    async (search: string = "") => {
      setLoading(true);
      try {
        const res = await fetchListings(search);
        setListings(res.data);
      } catch (error) {
        console.error("Failed to fetch listings:", error);
      } finally {
        setLoading(false);
      }
    },
    [searchText]
  );

  // Debounced Search
  useEffect(() => {
    if (!initialLoad) {
      const timeout = setTimeout(() => {
        loadListings(searchText);
      }, 1000); // debounce delay

      return () => clearTimeout(timeout);
    }
  }, [searchText, loadListings]);

  useEffect(() => {
    loadListings(); // initial load
    setInitialLoad(false);
  }, [loadListings]);

  const columns = [
    { title: "ID", dataIndex: "id" },
    { title: "Listings", dataIndex: "name" },
    { title: "Location", dataIndex: "location" },
    { title: "Status", dataIndex: "status" },
  ];

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <div className="flex gap-4 items-center">
          <ViewComfyOutlinedIcon />
          <h1 className="text-xl font-semibold">Listings</h1>
        </div>
      </div>
      <div className="p-6 bg-white rounded-xl shadow">
        {selectedListing && selectedListingName ? (
          <>
            <div className="mb-6 flex items-center text-sm text-gray-500">
              <button
                onClick={() => {
                  setSelectedListing(undefined);
                  setSelectedListingName(undefined);
                }}
                className="text-blue-600 hover:underline flex items-center gap-1"
              >
                ← Back to Listings
              </button>
            </div>
            <ListingDetailPage
              selectedListing={selectedListing}
              selectedListingName={selectedListingName}
            />
          </>
        ) : (
          <>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-500">
                {listings.length} listings
              </span>
              <Search
                placeholder="Search"
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
                className="w-60"
              />
            </div>

            {loading ? (
              <Spin />
            ) : (
              <Table
                columns={columns}
                dataSource={listings}
                className="cursor-pointer"
                rowKey={(record: { id: string; name: string }) =>
                  `${record.id}-${record.name}`
                }
                onRow={(record) => ({
                  onClick: () => {
                    setSelectedListing(record.id);
                    setSelectedListingName(record.name);
                  },
                })}
                pagination={false}
              />
            )}
          </>
        )}
      </div>
    </>
  );
}
