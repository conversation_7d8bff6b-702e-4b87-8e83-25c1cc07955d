import React, { useState, useEffect, useCallback } from "react";
import { Select, Input, DatePicker } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { fetchListings } from "../../../../services/targetAPI";
import { fetchReservationList } from "../../../../services/bookingAPI";

const { Option } = Select;

interface Listing {
  uid: string;
  name: string;
}

interface Booking {
  listingUID: string;
  date: string;
  channel: string;
  guestName: string;
  amount: number;
}

interface Rate {
  rate: number;
  minNights?: number;
}

interface Rates {
  [listingUID: string]: {
    [date: string]: Rate;
  };
}

const Calendar: React.FC = () => {
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [searchUID, setSearchUID] = useState<string>("");
  const [listings, setListings] = useState<Listing[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [rates, setRates] = useState<Rates>({});
  const [blockedNights, setBlockedNights] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // Fetch listings data
  const loadListings = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetchListings(searchUID);
      if (response.data && Array.isArray(response.data)) {
        // Transform the data to match the Listing interface
        const formattedListings = response.data.map((item: any) => ({
          uid: item.listingid || item.id,
          name: item.internal_listing_name || item.name
        }));
        setListings(formattedListings);
      }
    } catch (error) {
      console.error("Failed to fetch listings:", error);
    } finally {
      setLoading(false);
    }
  }, [searchUID]);

  // Fetch bookings data
  const loadBookings = useCallback(async () => {
    try {
      // Format dates for API request
      const startDate = `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-01`;
      const lastDay = new Date(selectedYear, selectedMonth + 1, 0).getDate();
      const endDate = `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${lastDay}`;

      const response = await fetchReservationList({
        params: {
          arrival_date_after: startDate,
          arrival_date_before: endDate
        }
      });

      if (response && response.results) {
        // Transform the data to match the Booking interface
        const formattedBookings = response.results.map((booking: any) => ({
          listingUID: booking.listing_id || booking.property_id,
          date: booking.arrival_date,
          channel: booking.channel || 'direct',
          guestName: booking.guest_name || 'Guest',
          amount: booking.total_amount || 0
        }));
        setBookings(formattedBookings);
      }
    } catch (error) {
      console.error("Failed to fetch bookings:", error);
    }
  }, [selectedMonth, selectedYear]);

  // Load data when component mounts or when search/month/year changes
  useEffect(() => {
    loadListings();
  }, [loadListings]);

  useEffect(() => {
    if (listings.length > 0) {
      loadBookings();
    }
  }, [listings, loadBookings]);

  // Generate calendar days for the selected month
  const generateCalendarDays = (): (number | null)[] => {
    const firstDay = new Date(selectedYear, selectedMonth, 1);
    const lastDay = new Date(selectedYear, selectedMonth + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    const days: (number | null)[] = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }
    
    return days;
  };

  const renderCalendarDay = (day: number | null, listingUID: string): JSX.Element => {
    if (!day) return <div className="h-20 border border-gray-200"></div>;

    // Format date consistently (YYYY-MM-DD)
    const dateKey = `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const dayRate = rates[listingUID]?.[dateKey];
    const isBlocked = blockedNights.includes(dateKey);
    const dayBooking = bookings.find(booking =>
      booking.listingUID === listingUID &&
      booking.date === dateKey
    );
    
    return (
      <div className={`h-20 border border-gray-200 p-1 ${isBlocked ? 'bg-gray-300' : 'bg-white'}`}>
        <div className="text-xs font-medium">{day}</div>
        
        {/* Rate display */}
        {dayRate && (
          <div className="text-xs text-blue-600 font-medium">
            ${dayRate.rate}
          </div>
        )}
        
        {/* Minimum night restriction */}
        {dayRate?.minNights && (
          <div className="text-xs text-gray-500">
            Min: {dayRate.minNights}n
          </div>
        )}
        
        {/* Booking information */}
        {dayBooking && (
          <div className="text-xs bg-green-100 p-1 rounded">
            {/* Channel icon */}
            <div className="flex items-center gap-1">
              <span className="text-xs">{getChannelIcon(dayBooking.channel)}</span>
              <span className="truncate">{dayBooking.guestName}</span>
            </div>
            <div className="text-xs font-medium">${dayBooking.amount}</div>
          </div>
        )}
      </div>
    );
  };

  const getChannelIcon = (channel: string): string => {
    // Return appropriate icon based on channel (Airbnb, Booking.com, etc.)
    const icons: { [key: string]: string } = {
      'airbnb': '🏠',
      'booking': '🏨',
      'vrbo': '🏡',
      'direct': '📞'
    };
    return icons[channel] || '📅';
  };

  const filteredListings = listings.filter(listing => 
    searchUID ? listing.uid.toLowerCase().includes(searchUID.toLowerCase()) : true
  );

  return (
    <div className="h-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Calendar</h1>
        
        {/* Filters */}
        <div className="flex gap-4 mb-4">
          {/* Search by UID */}
          <div className="flex-1">
            <Input
              placeholder="Search listing by UID"
              prefix={<SearchOutlined />}
              value={searchUID}
              onChange={(e) => setSearchUID(e.target.value)}
              className="max-w-xs"
            />
          </div>
          
          {/* Month dropdown */}
          <Select
            value={selectedMonth}
            onChange={(value: number) => setSelectedMonth(value)}
            className="w-40"
          >
            {[
              'January', 'February', 'March', 'April', 'May', 'June',
              'July', 'August', 'September', 'October', 'November', 'December'
            ].map((month, index) => (
              <Option key={index} value={index}>{month}</Option>
            ))}
          </Select>
          
          {/* Year selector */}
          <Select
            value={selectedYear}
            onChange={(value: number) => setSelectedYear(value)}
            className="w-24"
          >
            {[2023, 2024, 2025, 2026].map(year => (
              <Option key={year} value={year}>{year}</Option>
            ))}
          </Select>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="space-y-6">
        {loading && (
          <div className="text-center py-8">
            <div className="text-gray-500">Loading listings...</div>
          </div>
        )}

        {!loading && filteredListings.length === 0 && (
          <div className="text-center py-8">
            <div className="text-gray-500">
              {searchUID ? 'No listings found matching your search.' : 'No listings available. Please check your data connection or contact support.'}
            </div>
          </div>
        )}

        {!loading && filteredListings.map((listing) => (
          <div key={listing.uid} className="bg-white rounded-lg shadow-sm border">
            {/* Listing Header */}
            <div className="p-4 border-b bg-gray-50">
              <h3 className="font-semibold text-lg">
                UID: {listing.uid} - {listing.name}
              </h3>
            </div>
            
            {/* Calendar Grid */}
            <div className="p-4">
              {/* Day headers */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                  <div key={day} className="text-center text-sm font-medium text-gray-500 p-2">
                    {day}
                  </div>
                ))}
              </div>
              
              {/* Calendar days */}
              <div className="grid grid-cols-7 gap-1">
                {generateCalendarDays().map((day, index) => (
                  <div key={index}>
                    {renderCalendarDay(day, listing.uid)}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Calendar;