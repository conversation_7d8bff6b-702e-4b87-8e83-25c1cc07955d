import Tooltip from "@mui/material/Tooltip";
import Avatar from "@mui/material/Avatar";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../redux/store";
import { getRandomColor } from "../utils"; // make sure this path is correct
import SmartToyOutlinedIcon from "@mui/icons-material/SmartToyOutlined";
import DOMPurify from "dompurify";
import { useState } from "react";
import TranslateIcon from "@mui/icons-material/Translate";
import { translateToEn } from "../../../../services/chatAPI";
import { languageNames } from "./constants";

const formatContent = (content: string) => {
  return content.replace(/\n/g, "<br />").replace(/\t/g, "&emsp;"); // tab spacing
};

const AIIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth="1.8"
    stroke="currentColor"
    className="w-5 h-5 text-blue-300"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"
    />
  </svg>
);

const ApprovalBadge = () => (
  <span className="absolute -top-2 left-3 bg-yellow-600 text-white text-xs font-semibold px-2 py-1 rounded-md shadow-md cursor-pointer hover:scale-105 transition-transform whitespace-nowrap">
    Needs Approval
  </span>
);

const MessageBubble = ({
  message,
  setModalOpen,
  handleOpenModal,
  convName,
}: {
  message: any;
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleOpenModal: (message: any) => void;
  convName: string | undefined;
}) => {
  const isGuest = message.sender === "guest";
  const isAI = message.ai_generated;
  const needsApproval = message.needs_approval;

  const user = useSelector((state: RootState) => state.auth.user);

  const senderName = isGuest
    ? convName || "Guest"
    : message.sender_name || user?.name?.toLowerCase() || "Unknown";

  const avatarColor =
    senderName === "AI_Bot" ? "#A1E3F9" : getRandomColor(senderName);
  const [translatedText, setTranslatedText] = useState<string | null>(null);
  const [showOriginal, setShowOriginal] = useState(false);
  const [loading, setLoading] = useState(false);

  const shouldTranslate = message.language && message.language !== "en";

  const handleTranslate = async () => {
    setLoading(true);
    try {
      const response = await translateToEn(message.content, message.language);
      setTranslatedText(response.data.translation);
      setShowOriginal(false); // default to showing translated version
    } catch (err) {
      console.error("Translation failed", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className={`flex min-w-12 ${isGuest ? "justify-start" : "justify-end"} px-2 py-2`}
    >
      {/* Avatar */}
      {isGuest && (
        <div className="mr-2 mt-auto">
          <Tooltip title={senderName} placement="top">
            <Avatar
              sx={{
                bgcolor: avatarColor,
                color: "white",
                width: 32,
                height: 32,
                fontSize: "0.9rem",
                fontWeight: "bold",
              }}
            >
              {senderName.charAt(0)}
            </Avatar>
          </Tooltip>
        </div>
      )}

      <div
        className={`relative max-w-xs sm:max-w-md min-w-[160px] p-3 rounded-xl  mb-2 transition-all duration-200 break-words border font-semibold
          ${isGuest ? "bg-[#f1f1f1] text-[#434343] border-gray-100" : "bg-[#c8d1faba] text-gray-700 "}
          ${needsApproval ? "border border-yellow-500 !bg-yellow-50 !text-gray-900" : ""}
        `}
      >
        {/* AI Indicator */}
        {isAI && (
          <div className="absolute top-1 right-1 opacity-80">
            <Tooltip title="AI generated content" placement="top">
              <span>
                <AIIcon />
              </span>
            </Tooltip>
          </div>
        )}

        {/* Needs Approval Badge */}
        {needsApproval && (
          <div onClick={() => handleOpenModal(message)}>
            <ApprovalBadge />
          </div>
        )}

        {/* Message Content */}
        <div
          className="text-sm leading-relaxed break-words"
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(
              formatContent(
                translatedText && !showOriginal
                  ? translatedText
                  : message.content
              )
            ),
          }}
        />

        {shouldTranslate && !translatedText && (
          <button
            onClick={handleTranslate}
            className="text-xs text-blue-500 mt-2 flex items-center gap-1 hover:underline"
          >
            <TranslateIcon fontSize="small" />
            Translate to English
          </button>
        )}

        {translatedText && (
          <div className="mt-2 flex flex-col gap-1">
            <p className="text-[11px] text-gray-400 italic">
              {showOriginal
                ? `Original message in ${languageNames[message.language]}`
                : `Translated from ${languageNames[message.language]}`}
            </p>
            <button
              onClick={() => setShowOriginal((prev) => !prev)}
              className="text-[11px] text-blue-500 hover:underline self-start"
            >
              {showOriginal ? "Show Translation" : "Show Original"}
            </button>
          </div>
        )}

        {/* Timestamp */}
        <p className="text-xs text-right mt-1 text-gray-500">
          {dayjs(
            message.sender === "guest" ? message.received_at : message.sent_at
          ).format("h:mm A")}
        </p>
      </div>

      {/* Avatar on right for AI */}
      {!isGuest && (
        <div className="ml-2 mt-auto">
          <Tooltip
            title={senderName === "AI_Bot" ? "Chatbot" : senderName}
            placement="top"
          >
            <Avatar
              sx={{
                bgcolor: avatarColor,
                color: "white",
                width: 32,
                height: 32,
                fontSize: "0.9rem",
                fontWeight: "bold",
              }}
            >
              {senderName === "AI_Bot" ? (
                <SmartToyOutlinedIcon />
              ) : (
                senderName.charAt(0)
              )}
            </Avatar>
          </Tooltip>
        </div>
      )}
    </div>
  );
};

export default MessageBubble;
