import React, { useCallback, useEffect, useState } from "react";
import {
  RightOutlined,
  DownOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import SourceIcon from "@mui/icons-material/Source";
import LayersIcon from "@mui/icons-material/Layers";
import HolidayVillageIcon from "@mui/icons-material/HolidayVillage";
import {
  deleteFolder<PERSON>pi,
  fetchFoldersApi,
  renameFolder<PERSON><PERSON>,
} from "../../../services/targetAPI";
import { Dropdown, Input, Menu, Modal } from "antd";

type SidebarProps = {
  selectedTab: string;
  setSelectedTab: (tab: "Sources" | "Listings" | "Content") => void;
  selectedContentTab?: string | number | undefined;
  setSelectedContentTab: React.Dispatch<
    React.SetStateAction<string | number | undefined>
  >;
  folders: any[];
  loadFolders: () => Promise<void>;
};

const tabs = [
  { key: "Sources", icon: <SourceIcon className="text-lg" /> },
  { key: "Listings", icon: <HolidayVillageIcon className="text-lg" /> },
  { key: "Content", icon: <LayersIcon className="text-lg" /> },
];

const contentSubTabs = [
  { id: "1", name: "Overview" },
  { id: "2", name: "Policies" },
  { id: "3", name: "Amenities" },
  { id: "4", name: "Nearby Attractions" },
  { id: "5", name: "FAQs" },
];

export default function Sidebar({
  selectedTab,
  setSelectedTab,
  selectedContentTab,
  setSelectedContentTab,
  folders,
  loadFolders,
}: SidebarProps) {
  const [editingFolder, setEditingFolder] = useState<any | null>(null);
  const [renamedFolder, setRenamedFolder] = useState("");
  const [deletingFolder, setDeletingFolder] = useState<any | null>(null);

  const handleEdit = (folder: any) => {
    setEditingFolder(folder);
    setRenamedFolder(folder.name);
  };

  const handleSaveEdit = async () => {
    try {
      const response = await renameFolderApi(editingFolder?.id, renamedFolder);
      loadFolders();
    } catch (error) {
      console.error(error);
    } finally {
      setEditingFolder(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingFolder(null);
    setRenamedFolder("");
  };

  const handleDelete = (folder: any) => {
    setDeletingFolder(folder);
  };

  const confirmDelete = async () => {
    try {
      const response = await deleteFolderApi(deletingFolder?.id);
      loadFolders();
    } catch (error) {
      console.error(error);
    } finally {
      setDeletingFolder(null);
    }
  };

  const cancelDelete = () => {
    setDeletingFolder(null);
  };

  const getMenu = (folder: any) => (
    <Menu>
      <Menu.Item
        key="edit"
        icon={<EditOutlined />}
        onClick={() => handleEdit(folder)}
      >
        Edit
      </Menu.Item>
      <Menu.Item
        key="delete"
        icon={<DeleteOutlined />}
        danger
        onClick={() => handleDelete(folder)}
      >
        Delete
      </Menu.Item>
    </Menu>
  );

  return (
    <div className="w-60 bg-white border-r px-4 py-6 min-h-screen h-full">
      <h2 className="text-xl font-bold mb-6">Knowledge Base</h2>
      <ul className="space-y-2">
        {tabs.map((tab) => {
          const isSelected = selectedTab === tab.key;
          const isContent = tab.key === "Content";

          return (
            <React.Fragment key={tab.key}>
              <li
                className={`cursor-pointer flex items-center justify-between px-3 py-2 rounded-lg transition-all ${
                  isSelected
                    ? "bg-white border shadow-sm font-medium"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
                onClick={() => {
                  setSelectedTab(tab.key as any);
                  setSelectedContentTab(undefined);
                }}
              >
                <div className="flex items-center gap-2">
                  {tab.icon}
                  <span>{tab.key}</span>
                </div>
                {isSelected && isContent ? (
                  <DownOutlined className="text-xs text-gray-500" />
                ) : isSelected ? (
                  <RightOutlined className="text-xs text-gray-500" />
                ) : null}
              </li>

              {isSelected && isContent && (
                <ul className="ml-4 mt-2 space-y-1">
                  {folders.map((sub: any) => {
                    const isSubSelected = selectedContentTab === sub.id;

                    return (
                      <li
                        key={`Content-${sub.id}`}
                        className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer transition-all group ${
                          isSubSelected
                            ? "bg-blue-50 text-blue-800 font-medium border border-blue-200"
                            : "text-gray-700 hover:bg-gray-100"
                        }`}
                        onClick={() => setSelectedContentTab(sub.id)}
                      >
                        <span className="truncate">{sub.name}</span>

                        <Dropdown
                          overlay={getMenu(sub)}
                          trigger={["click"]}
                          placement="bottomRight"
                        >
                          <MoreOutlined
                            onClick={(e) => e.stopPropagation()}
                            className={`text-base opacity-70 group-hover:opacity-100 hover:text-gray-800 ${
                              isSubSelected ? "text-blue-500" : "text-gray-400"
                            }`}
                          />
                        </Dropdown>
                      </li>
                    );
                  })}
                </ul>
              )}
            </React.Fragment>
          );
        })}
      </ul>
      {/* Edit Folder Modal */}
      <Modal
        open={!!editingFolder}
        title="Rename Folder"
        onCancel={handleCancelEdit}
        onOk={handleSaveEdit}
        okText="Save"
        cancelText="Cancel"
      >
        <Input
          value={renamedFolder}
          onChange={(e) => setRenamedFolder(e.target.value)}
          placeholder="Enter new folder name"
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        open={!!deletingFolder}
        title="Delete Folder"
        onOk={confirmDelete}
        onCancel={cancelDelete}
        okText="Delete"
        okButtonProps={{ danger: true }}
        cancelText="Cancel"
      >
        <p>
          Are you sure you want to delete{" "}
          <strong>{deletingFolder?.name}</strong>?
        </p>
      </Modal>
    </div>
  );
}
