import React from "react";
import { Select, Tag } from "antd";
import type { SelectProps } from "antd";

type TagRender = SelectProps["tagRender"];

const options: SelectProps["options"] = [
  { value: "ownerStay", label: "Owner Stay" },
  { value: "new", label: "New" },
  { value: "modified", label: "Modified" },
];

const tagRender: TagRender = (props) => {
  const { label, value, closable, onClose } = props;
  const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  let tagColor = "cyan";
  if (value === "modified") {
    tagColor = "volcano";
  } else if (value === "new") {
    tagColor = "geekblue";
  } else if (value === "ownerStay") {
    tagColor = "lime";
  }

  return (
    <Tag
      color={tagColor}
      onMouseDown={onPreventMouseDown}
      closable={closable}
      onClose={onClose}
      style={{ marginInlineEnd: 4 }}
    >
      {label?.toString().toUpperCase()}
    </Tag>
  );
};

const StatusDropdown: React.FC = () => (
  <Select
    allowClear
    mode="multiple"
    tagRender={tagRender}
    placeholder="Filter listings"
    defaultValue={[]}
    style={{ width: "100%", height: "100%" }}
    options={options}
    dropdownStyle={{ maxHeight: "300px", overflowY: "auto" }} // Limit dropdown height
    maxTagCount="responsive" // Adjust max tags displayed in the select input
    maxTagPlaceholder={(omittedValues) => `+${omittedValues.length} more`} // Display remaining tags
  />
);

export default StatusDropdown;
