import { useCallback, useEffect, useState } from "react";
import React from "react";
import { useSearchParams } from "react-router-dom";
import Sidebar from "./Sidebar";
import SourceSection from "./Source";
import Listing from "./Listing";
import Content from "./Content";
import { fetchFoldersApi } from "../../../services/targetAPI";

const ListingsDashboard = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const tabParam = searchParams.get("tab") as
    | "Sources"
    | "Listings"
    | "Content";
  const defaultTab: "Sources" | "Listings" | "Content" = [
    "Sources",
    "Listings",
    "Content"
  ].includes(tabParam || "")
    ? (tabParam as "Sources" | "Listings" | "Content")
    : "Sources";

  const [selectedTab, setSelectedTab] = useState<
    "Sources" | "Listings" | "Content"
  >(defaultTab);

  const [selectedContentTab, setSelectedContentTab] = useState<
    string | number | undefined
  >(undefined);

  const [folders, setFolders] = useState([]);
  const [loading, setLoading] = useState(false);

  const loadFolders = useCallback(async () => {
    setLoading(true);
    try {
      const res = await fetchFoldersApi();
      setFolders(res.data);
    } catch (error) {
      console.error("Failed to fetch folders:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadFolders();
  }, []);

  useEffect(() => {
    if (tabParam !== selectedTab) {
      setSearchParams({ tab: selectedTab });
    }
  }, [selectedTab, setSearchParams]);

  return (
    <div
      className="flex overflow-hidden"
      style={{ height: "calc(100vh - 65px)" }}
    >
      <Sidebar
        selectedTab={selectedTab}
        setSelectedTab={setSelectedTab}
        selectedContentTab={selectedContentTab}
        setSelectedContentTab={setSelectedContentTab}
        folders={folders}
        loadFolders={loadFolders}
      />
      <div className="flex-1 p-6 bg-gray-50 overflow-y-auto">
        {selectedTab === "Sources" && <SourceSection />}
        {selectedTab === "Listings" && <Listing />}
        {selectedTab === "Content" && (
          <Content
            selectedContentTab={selectedContentTab}
            loadFolders={loadFolders}
            folders={folders}
          />
        )}
      </div>
    </div>
  );
};

export default ListingsDashboard;
