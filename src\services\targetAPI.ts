import axios from "axios";
import apiClient from "./apiClient";

export type ChannelParams = {
  year: string | Number;
  listing_name: string;
};
export type ArticleListParams = {
  folder?: string | number | 'all';
  search?: string;
  content_type?: 'internal_article' | 'public_article';
  page?: number;
  page_size?: number;
  ordering?: string;
};

const fetch = (
  page = 1,
  per_page = 10,
  activeSelection = "active",
  searchValue = ""
) =>
  apiClient.get(
    `/api/properties/targets?tab=${activeSelection}&search=${searchValue}&page=${page}&per_page=${per_page}`
  );

export const fetchConfigAPI = async (
  offset: number,
  limit: number,
  search: string = ""
) => {
  const params = new URLSearchParams({
    offset: offset.toString(),
    limit: limit.toString(),
    search,
  });
  const response = await apiClient.get(
    `/api/properties/config?${params.toString()}`
  );
  return response.data;
};

const fetchPropertyTarget = async ({
  propertyID,
  params,
}: {
  propertyID: string | number;
  params: any;
}) => {
  return await apiClient.get(`/api/revenue-targets/${propertyID}/`, { params });
};

const update = ({
  propertyID,
  payload,
}: {
  propertyID: string | number;
  payload: any;
}) => apiClient.post(`/api/revenue-targets/${propertyID}/update`, payload);

const configUpdateAPI = ({ payload }: { payload: any }) =>
  apiClient.post(`/api/properties/config/update/`, payload);

const customPMFeesTable = async ({
  propertyID,
  params,
}: {
  propertyID: string | number;
  params: any;
}) => {
  return await apiClient.get(`/api/pmfees/${propertyID}/`, { params });
};

export const fetchChannelData = async ({
  params,
}: {
  params: ChannelParams;
}) => {
  try {
    const response = await apiClient.get(`/api/channel/`, {
      params,
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching channel list", error);
    throw error;
  }
};

const fetchListingDetails = async (propertyID: string | number) => {
  return await apiClient.get(`/api/listing/${propertyID}/`);
};

const updateListingDetails = async (
  listingId: string | number,
  updatedData: any
) => {
  return await apiClient.put(`api/listing/update/${listingId}/`, updatedData);
};

const createSource = ({ payload }: { payload: any }) =>
  apiClient.post(`/api/external-sources/`, payload);

const fetchSourcesApi = async () => {
  return await apiClient.get(`/api/sources/overview/`);
};

const DeleteSourceAPI = async (source_id: string | number) => {
  return await apiClient.delete(`/api/external-source/${source_id}/`);
};
const validateSourceNameAPI = async (name: string) => {
  return await apiClient.get(`/api/validate-source-name/?name=${name}`);
};

// Modify this function to accept a `deleted` flag
const fetchListings = async (search: string = "", deleted: boolean = false) => {
  const deletedParam = deleted ? "true" : "false";
  return await apiClient.get(`/api/listings/summary/?search=${search}&deleted=${deletedParam}`);
};


const syncTargetSheetsAPI = async () => {
  return await apiClient.post(`/api/sync-target-sheets/`);
};

const getListingDetails = async (
  listingId: string,
  activeTab: string = "details"
) => {
  const response = await apiClient.get(`/api/listings/${listingId}/`, {
    params: { activeTab },
  });
  return response.data;
};

const updateListingDetailsAPI = async (
  listingId: string | number,
  updatedData: any
) => {
  return await apiClient.put(`api/listings/${listingId}/edit/`, updatedData);
};

const testSourceConnection = async (payload: any) =>
  apiClient.post(`/api/proxy-webhook/`, payload);

const fetchFoldersApi = async () => {
  return await apiClient.get(`/api/folders/`);
};

const createFolder = async (payload: any) =>
  apiClient.post(`/api/create/folder/`, payload);

const renameFolderApi = (id: number | string, name: string) => {
  return apiClient.patch(`/api/folders/${id}/rename/`, { name });
};

const deleteFolderApi = (id: number | string) => {
  return apiClient.delete(`/api/folders/${id}/delete/`);
};

const createArticle = async (payload: any) => {
  return await apiClient.post(`/api/create/article/`, payload);
};

const fetchArticles = async (params: ArticleListParams = {}) => {
  try {
    const response = await apiClient.get('/api/articles/', { params });
    return response.data;
  } catch (error) {
    console.error("Error fetching articles", error);
    throw error;
  }
};

// Fetch single article by ID
const fetchArticleById = async (articleId: string | number) => {
  try {
    const response = await apiClient.get(`/api/articles/${articleId}/`);
    return response.data;
  } catch (error) {
    console.error("Error fetching article", error);
    throw error;
  }
};

// Update article by ID
const updateArticle = async (articleId: string | number, payload: any) => {
  try {
    const response = await apiClient.put(`/api/articles/${articleId}/`, payload);
    return response.data;
  } catch (error) {
    console.error("Error updating article", error);
    throw error;
  }
};


export {
  fetch,
  fetchPropertyTarget,
  update,
  customPMFeesTable,
  configUpdateAPI,
  fetchListingDetails,
  updateListingDetails,
  createSource,
  fetchSourcesApi,
  DeleteSourceAPI,
  validateSourceNameAPI,
  fetchListings,
  getListingDetails,
  updateListingDetailsAPI,
  testSourceConnection,
  fetchFoldersApi,
  createFolder,
  renameFolderApi,
  deleteFolderApi,
  createArticle,
  fetchArticles,
  fetchArticleById,
  updateArticle,
  syncTargetSheetsAPI
};
