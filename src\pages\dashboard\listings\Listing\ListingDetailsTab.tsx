import React, { useCallback, useEffect, useState } from "react";
import {
  getListingDetails,
  updateListingDetails,
  updateListingDetailsAPI,
} from "../../../../services/targetAPI";

const BUILDING_FIELDS = [
  { key: "name", label: "Name" },
  { key: "tower", label: "Tower" },
  { key: "apartment number", label: "Apartment Number" },
  { key: "floor", label: "Floor" },
];

export default function ListingDetailsTab({
  selectedListing,
}: {
  selectedListing: string;
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);

  const [listingDetails, setListingDetails] = useState<any | null>(null);
  const [editedDetails, setEditedDetails] = useState<any | null>(null);
  const parseBuildingAmenities = (
    amenities: string | string[] | undefined | null
  ): string[] => {
    if (!amenities) return [];

    // If it's already an array, return it as is
    if (Array.isArray(amenities)) {
      return amenities;
    }

    // If it's a string, parse it
    if (typeof amenities === "string") {
      return amenities
        .split(/[,\n]/) // Split by comma or newline
        .map((item: string) => item.trim()) // Remove extra whitespace
        .filter((item: string) => item.length > 0); // Remove empty strings
    }

    return [];
  };

  const loadListingDetails = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getListingDetails(selectedListing, "details");
      setListingDetails(res);
    } catch (error) {
      console.error("Failed to fetch listings:", error);
    } finally {
      setLoading(false);
    }
  }, [selectedListing]);

  const onSave = useCallback(
    async (payload: any) => {
      setIsEditing(true);
      try {
        const bodyData = {
          building: payload.building?.name, // mapped from `name`
          tower: payload.building?.tower,
          apartment_number: payload.building["apartment number"],
          floor: payload.building?.floor,
          building_amanities: payload.building_amanities, // correct spelling
          parking_slot: payload.parkingSlot, // match backend's camelCase
        };
        const res = await updateListingDetailsAPI(selectedListing, bodyData);
        loadListingDetails();
      } catch (error) {
        console.error("Failed to save listing:", error);
      } finally {
        setIsEditing(false);
      }
    },
    [selectedListing, loadListingDetails]
  );

  const handleChange = (field: string, value: string) => {
    setEditedDetails((prev: any) => ({
      ...prev,
      building: {
        ...prev.building,
        [field]: value,
      },
    }));
  };

  useEffect(() => {
    if (listingDetails) {
      setEditedDetails({
        building: { ...listingDetails.building },
        building_amanities: parseBuildingAmenities(
          listingDetails.buildingAmenities
        ),
        parkingSlot: listingDetails.parkingSlot || "",
      });
    }
  }, [listingDetails]);

  useEffect(() => {
    selectedListing && loadListingDetails(); // initial load
  }, [selectedListing]);

  const handleAmenitiesChange = (index: number, value: string) => {
    const updatedAmenities = [...editedDetails.building_amanities];
    updatedAmenities[index] = value;
    setEditedDetails((prev: any) => ({
      ...prev,
      building_amanities: updatedAmenities,
    }));
  };

  const handleSave = () => {
    setIsEditing(false);
    const cleanedDetails = {
      ...editedDetails,
      building_amanities: editedDetails?.building_amanities?.filter(
        (a: string) => a.trim() !== ""
      ),
    };
    if (onSave) onSave(cleanedDetails);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedDetails({
      building: { ...listingDetails?.building },
      building_amanities: [...listingDetails?.buildingAmenities],
      parkingSlot: listingDetails?.parkingSlot,
    });
  };

  return (
    <div className="space-y-10 text-gray-800 text-sm">
      {/* Listing Basics */}
      {listingDetails && listingDetails?.basics ? (
        <>
          <section>
            <h2 className="text-lg font-semibold mb-2">Listing Basics</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-2 gap-x-8">
              <p>
                <span className="font-medium">UID:</span>{" "}
                {listingDetails?.basics?.uid || ""}
              </p>
              <p>
                <span className="font-medium">Listing Name:</span>{" "}
                {listingDetails?.basics?.name || ""}
              </p>
              <p>
                <span className="font-medium">Property Type:</span>{" "}
                {listingDetails?.basics?.propertyType || ""}
              </p>
              <p>
                <span className="font-medium">Room Type:</span>{" "}
                {listingDetails?.basics?.roomType || ""}
              </p>
              <p>
                <span className="font-medium">Bedrooms:</span>{" "}
                {listingDetails?.basics?.bedrooms || "Studio"}
              </p>
              <p>
                <span className="font-medium">Beds:</span>{" "}
                {listingDetails?.basics?.beds || ""}
              </p>
              <p>
                <span className="font-medium">Bathrooms:</span>{" "}
                {listingDetails?.basics?.bathrooms || ""}
              </p>
              <p>
                <span className="font-medium">Guests:</span>{" "}
                {listingDetails?.basics?.guests || ""}
              </p>
            </div>
          </section>

          {/* Description */}
          <section className="border-t">
            <h2 className="text-lg font-semibold mb-2 mt-2">
              Listing Description
            </h2>
            <p className="whitespace-pre-line text-gray-700 leading-relaxed">
              {listingDetails?.description || ""}
            </p>
          </section>

          {/* Location */}
          <section className="border-t">
            <h2 className="text-lg font-semibold mb-2 mt-2">Location</h2>
            <div className="space-y-1">
              <p>
                City - {listingDetails?.location?.city || ""} -{" "}
                {listingDetails?.location?.street || ""}
              </p>
              <p>Street - {listingDetails?.location?.street || ""}</p>
              <p>
                Location map -{" "}
                {listingDetails?.location?.mapUrl ? (
                  <a
                    href={listingDetails.location.mapUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {listingDetails.location.mapUrl}
                  </a>
                ) : (
                  ""
                )}
              </p>
            </div>
          </section>

          {/* Photos */}
          <section className="border-t">
            <h2 className="text-lg font-semibold mb-2 mt-2">Photos</h2>
            <div className="flex gap-4 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
              {listingDetails?.photos.map((img: any, idx: any) => (
                <div
                  key={idx}
                  className="min-w-[10rem] h-32 rounded-xl overflow-hidden border shadow-md"
                >
                  <img
                    src={img.url}
                    alt={`Photo ${idx + 1}`}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                  />
                </div>
              ))}
            </div>
          </section>

          {/* Amenities */}
          <section className="border-t">
            <h2 className="text-lg font-semibold mb-2 mt-2">Amenities</h2>
            <ul className="grid grid-cols-2 md:grid-cols-4 gap-y-2 gap-x-4 text-gray-700 list-disc pl-5">
              {listingDetails?.amanities?.map((item: any, idx: any) => (
                <li key={idx}>{item}</li>
              ))}
            </ul>
          </section>

          {/* Building */}
          <section className="border-t">
            <div className="flex items-center justify-between mt-2">
              <h2 className="text-lg font-semibold mb-3">Building</h2>
              {!isEditing && (
                <button
                  className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors"
                  onClick={() => setIsEditing(true)}
                >
                  Edit
                </button>
              )}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-2 gap-x-8">
              {BUILDING_FIELDS.map(({ key, label }) => (
                <p key={key}>
                  <span className="font-medium">{label}:</span>{" "}
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedDetails?.building[key] || ""}
                      onChange={(e) => handleChange(key, e.target.value)}
                      className="border px-2 py-1 rounded w-full"
                    />
                  ) : (
                    listingDetails?.building[key] || ""
                  )}
                </p>
              ))}
            </div>
          </section>

          {/* Building Amenities */}
          {/* <section>
            <span className="font-medium mb-2">Building Amenities</span>
            {isEditing ? (
              <div className="space-y-2">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {(editedDetails &&
                  editedDetails?.building_amanities &&
                  editedDetails?.building_amanities.length > 0
                    ? editedDetails?.building_amanities
                    : [""]
                  ) // show one empty input if none exist
                    .map((item: string, idx: number) => (
                      <input
                        key={idx}
                        type="text"
                        value={item}
                        onChange={(e) =>
                          handleAmenitiesChange(idx, e.target.value)
                        }
                        className="border px-2 py-1 rounded w-full"
                        placeholder={`Amenity ${idx + 1}`}
                      />
                    ))}
                </div>
                <button
                  onClick={() =>
                    setEditedDetails((prev: any) => ({
                      ...prev,
                      building_amanities: [
                        ...(prev.building_amanities || []),
                        "",
                      ],
                    }))
                  }
                  className="mt-2 text-blue-600 hover:underline text-sm"
                >
                  + Add Amenity
                </button>
              </div>
            ) : (
              <ul className="grid grid-cols-2 md:grid-cols-4 gap-y-2 gap-x-4 text-gray-700 list-disc pl-5">
                {listingDetails &&
                  listingDetails?.buildingAmenities &&
                  listingDetails?.buildingAmenities.map(
                    (item: string, idx: number) => <li key={idx}>{item}</li>
                  )}
              </ul>
            )}
          </section> */}
          <section>
            <span className="font-medium mb-2">Building Amenities</span>
            {isEditing ? (
              <div className="space-y-2">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {(editedDetails &&
                  editedDetails?.building_amanities &&
                  editedDetails?.building_amanities.length > 0
                    ? editedDetails?.building_amanities
                    : [""]
                  ) // show one empty input if none exist
                    .map((item: string, idx: number) => (
                      <input
                        key={idx}
                        type="text"
                        value={item}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          handleAmenitiesChange(idx, e.target.value)
                        }
                        className="border px-2 py-1 rounded w-full"
                        placeholder={`Amenity ${idx + 1}`}
                      />
                    ))}
                </div>
                <button
                  onClick={() =>
                    setEditedDetails((prev: any) => ({
                      ...prev,
                      building_amanities: [
                        ...(prev.building_amanities || []),
                        "",
                      ],
                    }))
                  }
                  className="mt-2 text-blue-600 hover:underline text-sm"
                >
                  + Add Amenity
                </button>
              </div>
            ) : (
              <ul className="grid grid-cols-2 md:grid-cols-4 gap-y-2 gap-x-4 text-gray-700 list-disc pl-5">
                {parseBuildingAmenities(listingDetails?.buildingAmenities).map(
                  (item: string, idx: number) => (
                    <li key={idx}>{item}</li>
                  )
                )}
              </ul>
            )}
          </section>

          {/* Parking */}
          <section>
            <span className="font-medium mb-3">Parking</span>
            {isEditing ? (
              <input
                type="text"
                value={editedDetails?.parkingSlot}
                onChange={(e) =>
                  setEditedDetails((prev: any) => ({
                    ...prev,
                    parkingSlot: e.target.value,
                  }))
                }
                className="border px-2 py-1 rounded w-full"
              />
            ) : (
              <p>{listingDetails.parkingSlot}</p>
            )}
          </section>

          {/* Save / Cancel Buttons */}
          {isEditing && (
            <div className="flex gap-4">
              <button
                className="px-4 py-1 bg-blue-600 text-white rounded"
                onClick={handleSave}
              >
                Save
              </button>
              <button
                className="px-4 py-1 border rounded text-gray-700"
                onClick={handleCancel}
              >
                Cancel
              </button>
            </div>
          )}
        </>
      ) : (
        <div className="space-y-6 animate-pulse">
          {/* Skeleton for Listing Basics */}
          <div className="h-6 w-1/3 bg-gray-300 rounded"></div>
          <div className="grid grid-cols-2 gap-4">
            {Array.from({ length: 8 }).map((_, idx) => (
              <div key={idx} className="h-4 w-full bg-gray-200 rounded"></div>
            ))}
          </div>

          {/* Skeleton for Description */}
          <div className="h-6 w-1/3 bg-gray-300 rounded mt-6"></div>
          <div className="h-20 w-full bg-gray-200 rounded"></div>

          {/* Skeleton for Location */}
          <div className="h-6 w-1/3 bg-gray-300 rounded mt-6"></div>
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, idx) => (
              <div key={idx} className="h-4 w-2/3 bg-gray-200 rounded"></div>
            ))}
          </div>

          {/* Skeleton for Photos */}
          <div className="h-6 w-1/3 bg-gray-300 rounded mt-6"></div>
          <div className="flex gap-4">
            {Array.from({ length: 4 }).map((_, idx) => (
              <div key={idx} className="w-40 h-32 bg-gray-200 rounded"></div>
            ))}
          </div>

          {/* Skeleton for Amenities */}
          <div className="h-6 w-1/3 bg-gray-300 rounded mt-6"></div>
          <div className="grid grid-cols-2 gap-4">
            {Array.from({ length: 6 }).map((_, idx) => (
              <div key={idx} className="h-4 w-full bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
