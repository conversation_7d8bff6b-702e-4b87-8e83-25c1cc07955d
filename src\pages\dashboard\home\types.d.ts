export interface AverageRevenueProps {
    data: {
      name: string;
      Revenue: number;
      ADR: number;
      LOS: number;
      "Lead-time": number;
      "PM Fees": number;
    }[];
    dataKey: "Revenue" | "ADR" | "LOS" | "Lead-time" | "PM Fees";
  }

  export interface BookingsProps {
    data: {
      name: string;
      Bookings: number;
      Nights: number;
      RevPAR: number;
      Occupancy: string;
      blocked: number;
    }[];
    dataKey: "Bookings" | "Nights" | "RevPAR" | "Occupancy" | "blocked";
    isBookedNight?:Boolean;
  }

  export interface PieChartComponentProps {
    data: { name: string; value: number }[];
    colors?: string[];
    innerRadius?: number;
    outerRadius?: number;
    dataKey?: string;
    cx?: string | number;
    cy?: string | number;
    width?: string | number;
    height?: string | number;
    activeIndex?: number;
    onPieEnter?: (index: number) => void;
    isNet?:Boolean;
  }

  export interface Property {
    id: number;
    name: string;
    revenue: number;
    gross_revenue: number;
    bookings: number;
  }

  export interface BookingSourceTableProps {
    data: Property[];
    columns?: ColumnsType<Property>;
    revenueSelection?: string;
  }