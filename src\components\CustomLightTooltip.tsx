import * as React from 'react';
import { styled } from '@mui/material/styles';
import Tooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';

// Define a reusable LightTooltip component
const LightTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.white,
    color: 'rgba(0, 0, 0, 0.87)',
    boxShadow: theme.shadows[1],
    fontSize: 11,
    borderRadius: 4,
    padding: '8px 12px',
  },
}));

// LightTooltipProps interface for type safety
interface LightTooltipProps extends TooltipProps {
  children: React.ReactElement;
}

// Reusable LightTooltip component
const CustomLightTooltip: React.FC<LightTooltipProps> = ({ children, ...props }) => {
  return <LightTooltip {...props}>{children}</LightTooltip>;
};

export default CustomLightTooltip;
