{"name": "pms-integrated-client", "version": "0.1.0", "private": true, "dependencies": {"@date-io/dayjs": "^3.0.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^6.1.1", "@mui/lab": "^6.0.0-beta.10", "@mui/material": "^6.1.1", "@mui/x-date-pickers": "^7.17.0", "@mui/x-tree-view": "^7.17.0", "@reduxjs/toolkit": "^2.2.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@toolpad/core": "^0.6.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.108", "@types/react": "^18.3.7", "@types/react-dom": "^18.3.0", "antd": "^5.21.0", "antd-style": "^3.7.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "framer-motion": "^12.7.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropdown-tree-select": "^2.8.0", "react-google-recaptcha": "^3.1.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.16.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "react-toastify": "^10.0.5", "recharts": "^2.12.7", "styled-components": "^6.1.13", "tailwindcss": "^3.4.12", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-google-recaptcha": "^2.1.9"}}