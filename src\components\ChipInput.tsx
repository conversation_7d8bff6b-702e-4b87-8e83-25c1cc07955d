import React from "react";
import { Tag } from "antd";
import CustomLightTooltip from "./CustomLightTooltip";

interface ChipInputProps {
  selectedItems: string[];
  onDelete: (item: string) => void;
}

const ChipInput: React.FC<ChipInputProps> = ({ selectedItems, onDelete }) => {
  const handleTagClose = (item: string) => {
    onDelete(item);
  };

  const tagRender = (item: string) => {
    let tagColor = "cyan";
    if (item === "airbnbOfficial") {
      tagColor = "volcano";
    } else if (item === "bookingcom") {
      tagColor = "geekblue";
    } else if (item === "ownerStay") {
      tagColor = "lime";
    }

    return (
      <CustomLightTooltip title={item}>
        <Tag
          color={tagColor}
          closable
          onClose={() => handleTagClose(item)}
          style={{ marginInlineEnd: 4 }}
        >
          {item.toUpperCase()}
        </Tag>
      </CustomLightTooltip>
    );
  };

  return (
    <div className="relative">
      {selectedItems?.length !== 0 ? (
        <div className="left-0 justify-center w-full max-h-24 overflow-y-auto bg-white border border-blue-300 rounded-md p-2">
          <div className="grid gap-2">
            {selectedItems.map((item) => tagRender(item))}
          </div>
        </div>
      ) : (
        <input
          type="text"
          value=""
          placeholder="Filter locations"
          className="border border-gray-300 max-h-24 rounded-md p-2 w-full focus:outline-none focus:ring "
          readOnly
        />
      )}
    </div>
  );
};

export default ChipInput;
