import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  fetchCalendarData,
  fetchAvailabilitySummary,
  formatDateForAPI,
  CalendarParams
} from "../../../services/calendarAPI";
import { fetchListings } from "../../../services/targetAPI";
import { Listing, Booking, Rate, Rates } from "./types";
import CalendarHeader from "./CalendarHeader";
import DateNavigator from "./DateNavigator";
import ListingRow from "./ListingRow";
import { Spin, Alert } from "antd";

const Calendar: React.FC = () => {
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [searchUID, setSearchUID] = useState<string>("");
  const [listings, setListings] = useState<Listing[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [rates, setRates] = useState<Rates>({});
  const [blockedNights, setBlockedNights] = useState<{ [listingId: string]: string[] }>({});
  const [blockedNotes, setBlockedNotes] = useState<{ [listingId: string]: { [date: string]: string } }>({});
  const [blockSpans, setBlockSpans] = useState<{ [listingId: string]: Array<{start: string, end: string, dates: string[], notes?: string}> }>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [navigationLoading, setNavigationLoading] = useState<boolean>(false);
  
  // Calculate offset to show the week containing today's date for current month
  const getCurrentWeekOffset = (month: number, year: number): number => {
    const today = new Date();
    const isCurrentMonth = today.getMonth() === month && today.getFullYear() === year;

    if (!isCurrentMonth) {
      return 0; // For non-current months, start from beginning
    }

    const currentDate = today.getDate();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const visibleDays = 7;

    // Calculate offset to show a week that includes today
    const idealOffset = Math.max(0, currentDate - 4); // Put today around middle of the week
    const maxOffset = Math.max(0, daysInMonth - visibleDays);

    return Math.min(idealOffset, maxOffset);
  };

  const [dateOffset, setDateOffset] = useState<number>(getCurrentWeekOffset(selectedMonth, selectedYear));
  const [listingColumnWidth, setListingColumnWidth] = useState<number>(256);
  const [isResizing, setIsResizing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [displayedListings, setDisplayedListings] = useState<Listing[]>([]);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [itemsPerLoad] = useState<number>(20); // Items to load per scroll
  const [isHorizontalScrolling, setIsHorizontalScrolling] = useState<boolean>(false);

  const VISIBLE_DAYS = 7; // Number of days to show at once

  // Generate visible dates based on current offset
  const getVisibleDates = (): Date[] => {
    const dates: Date[] = [];
    const startDate = new Date(selectedYear, selectedMonth, 1 + dateOffset);
    
    for (let i = 0; i < VISIBLE_DAYS; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      dates.push(date);
    }
    
    return dates;
  };

  const getDataDates = (): Date[] => {
    const displayDates = getVisibleDates();
    
    // Add previous date at the beginning for data fetching
    const previousDate = new Date(displayDates[0]);
    previousDate.setDate(previousDate.getDate() - 1);
    
    return [previousDate, ...displayDates];
  };

  const visibleDates = getVisibleDates();
  const dataDates = getDataDates(); 

  // Week navigation handlers (for arrow buttons) - move 7 days
  const handlePreviousWeek = async () => {
    setNavigationLoading(true);

    try {
      // Move back by 7 days
      const newOffset = dateOffset - 7;
      
      if (newOffset < 0) {
        // Go to previous month
        let targetMonth = selectedMonth;
        let targetYear = selectedYear;

        if (selectedMonth === 0) {
          targetMonth = 11;
          targetYear = selectedYear - 1;
        } else {
          targetMonth = selectedMonth - 1;
        }

        // Calculate the offset in previous month
        const daysInPrevMonth = new Date(targetYear, targetMonth + 1, 0).getDate();
        const targetOffset = Math.max(0, daysInPrevMonth + newOffset);

        setSelectedMonth(targetMonth);
        setSelectedYear(targetYear);
        setDateOffset(targetOffset);
      } else {
        setDateOffset(newOffset);
      }
    } finally {
      setTimeout(() => setNavigationLoading(false), 100);
    }
  };

  const handleNextWeek = async () => {
    setNavigationLoading(true);

    try {
      const daysInCurrentMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();
      const newOffset = dateOffset + 7;

      if (newOffset >= daysInCurrentMonth) {
        // Go to next month
        const daysOverflow = newOffset - daysInCurrentMonth;
        
        if (selectedMonth === 11) {
          setSelectedMonth(0);
          setSelectedYear(prev => prev + 1);
        } else {
          setSelectedMonth(prev => prev + 1);
        }
        setDateOffset(daysOverflow);
      } else {
        setDateOffset(newOffset);
      }
    } finally {
      setTimeout(() => setNavigationLoading(false), 100);
    }
  };

  // Day navigation handlers (for scrolling and keyboard) - move 1 day
  const handlePreviousDay = async () => {
    setNavigationLoading(true);

    try {
      // Move back by 1 day
      if (dateOffset === 0) {
        // Go to previous month's last possible day
        let targetMonth = selectedMonth;
        let targetYear = selectedYear;

        if (selectedMonth === 0) {
          targetMonth = 11;
          targetYear = selectedYear - 1;
        } else {
          targetMonth = selectedMonth - 1;
        }

        // Calculate the last day we can show in previous month
        const daysInPrevMonth = new Date(targetYear, targetMonth + 1, 0).getDate();
        const lastPossibleOffset = Math.max(0, daysInPrevMonth - VISIBLE_DAYS);

        setSelectedMonth(targetMonth);
        setSelectedYear(targetYear);
        setDateOffset(lastPossibleOffset);
      } else {
        // Move back by 1 day within current month
        setDateOffset(prev => Math.max(0, prev - 1));
      }
    } finally {
      setTimeout(() => setNavigationLoading(false), 100);
    }
  };

  const handleNextDay = async () => {
    setNavigationLoading(true);

    try {
      const daysInCurrentMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();
      const maxOffset = Math.max(0, daysInCurrentMonth - VISIBLE_DAYS);

      // Move forward by 1 day
      if (dateOffset >= maxOffset) {
        // Go to next month
        if (selectedMonth === 11) {
          setSelectedMonth(0);
          setSelectedYear(prev => prev + 1);
        } else {
          setSelectedMonth(prev => prev + 1);
        }
        setDateOffset(0);
      } else {
        // Move forward by 1 day within current month
        setDateOffset(prev => Math.min(maxOffset, prev + 1));
      }
    } finally {
      setTimeout(() => setNavigationLoading(false), 100);
    }
  };

  // Handle column resize
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);

    const startX = e.clientX;
    const startWidth = listingColumnWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const newWidth = Math.max(200, Math.min(500, startWidth + deltaX));
      setListingColumnWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [listingColumnWidth]);

  // Add global styles for resizing cursor
  useEffect(() => {
    if (isResizing) {
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    } else {
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    }

    return () => {
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // Calculate date range for API calls - optimized for 7-day loading with buffer for cross-day bookings
  const dateRange = useMemo(() => {
    const visibleDates = getDataDates();

    // Start 3 days before the first visible date to capture bookings that start earlier
    const startDate = new Date(visibleDates[0]);
    startDate.setDate(startDate.getDate() - 3);

    // End 3 days after the last visible date to capture bookings that extend beyond
    const endDate = new Date(visibleDates[visibleDates.length - 1]);
    endDate.setDate(endDate.getDate() + 3);

    return {
      startDate: formatDateForAPI(startDate),
      endDate: formatDateForAPI(endDate)
    };
  }, [selectedMonth, selectedYear, dateOffset]);

  // Load listings separately for better performance
  const loadListings = useCallback(async () => {
    try {
      const response = await fetchListings(searchUID, true);

      if (response.data && Array.isArray(response.data)) {
        const transformedListings: Listing[] = response.data.map((property: any) => ({
          uid: property.listingid || property.id,
          name: property.internal_listing_name || property.name
        }));

        setListings(transformedListings);
        // Initialize displayed listings with first batch
        setDisplayedListings(transformedListings.slice(0, itemsPerLoad));
        setHasMore(transformedListings.length > itemsPerLoad);
      }

    } catch (error) {
      console.error("Failed to fetch listings:", error);
      setError("Failed to load listings. Please try again.");
    }
  }, [searchUID]);

  // Load calendar data (availability and bookings) for current listings
 const loadCalendarData = useCallback(async () => {
  if (listings.length === 0) return;

  setLoading(true);
  setError(null);

  try {
    const propertyIds = listings.map(listing => listing.uid);

    const params: CalendarParams = {
      start_date: dateRange.startDate,
      end_date: dateRange.endDate,
      property_ids: propertyIds,
      page: 1,
      page_size: 56
    };

    const calendarData = await fetchCalendarData(params);
    const transformedBookings: Booking[] = [];
    const transformedRates: Rates = {};
    const transformedBlockedNights: { [listingId: string]: string[] } = {};
    const blockedNotes: { [listingId: string]: { [date: string]: string } } = {};

    // Process calendar data for each property
    Object.entries(calendarData.data.calendar_data).forEach(([listingId, propertyData]) => {
      // Find the property info by listingid (not database id)
      const property = calendarData.data.properties.find(p => p.listingid === listingId);
      if (!property) return;

      // ENHANCED: Process blocked dates - handle both "blocked" and "reserved" without reservations
      Object.entries(propertyData.availability).forEach(([date, availability]) => {
        const isActuallyBlocked = availability.status === 'blocked' || 
          (availability.status === 'reserved' && 
           (!propertyData.reservations || !propertyData.reservations[date]));

        if (isActuallyBlocked) {
          // Initialize array for this property if it doesn't exist
          if (!transformedBlockedNights[property.listingid]) {
            transformedBlockedNights[property.listingid] = [];
          }
          transformedBlockedNights[property.listingid].push(date);
          
          // Store notes for blocked dates with property association
          if (availability.notes) {
            if (!blockedNotes[property.listingid]) {
              blockedNotes[property.listingid] = {};
            }
            blockedNotes[property.listingid][date] = availability.notes;
          }
        }
      });

      // Process available dates - only those that are truly available (not blocked or reserved)
      Object.entries(propertyData.availability).forEach(([date, availability]) => {
        const isAvailable = availability.status === 'available' || 
          (availability.status === 'reserved' && 
           propertyData.reservations && 
           propertyData.reservations[date]);

        if (availability.status === 'available') {
          // Add to rates for available dates
          if (!transformedRates[property.listingid]) {
            transformedRates[property.listingid] = {};
          }
          transformedRates[property.listingid][date] = {
            rate: availability.price,
            minNights: availability.min_nights
          };
        }
      });

      // Process reservations data - only process actual reservations with booking details
      if (propertyData.reservations) {
        // Use a Map to track unique bookings by arrival_date + departure_date + guest_name + channel
        const uniqueBookings = new Map<string, any>();

        Object.entries(propertyData.reservations).forEach(([date, reservation]) => {
          // Only process if there are actual reservation details
          if (reservation && reservation.guest_name) {
            // Create a unique key for this booking
            const bookingKey = `${reservation.arrival_date}-${reservation.departure_date}-${reservation.guest_name}-${reservation.channel}`;

            // If this booking hasn't been processed yet, add it
            if (!uniqueBookings.has(bookingKey)) {
              uniqueBookings.set(bookingKey, {
                listingUID: property.listingid,
                date: date,
                channel: reservation.channel,
                guestName: reservation.guest_name,
                amount: reservation.total_price,
                arrival_date: reservation.arrival_date,
                departure_date: reservation.departure_date
              });
            }
          }
        });

        // Add all unique bookings to the transformed bookings array
        uniqueBookings.forEach(booking => {
          transformedBookings.push(booking);
        });
      }
    });

    // Analyze blocked dates to detect actual block spans (same as before)
    const detectedBlockSpans: { [listingId: string]: Array<{start: string, end: string, dates: string[], notes?: string}> } = {};

    Object.entries(transformedBlockedNights).forEach(([propertyId, blockedDates]) => {
      if (blockedDates.length > 0) {
        // Group consecutive blocked dates to find actual block spans
        const sortedDates = blockedDates.sort();
        const propertyBlockSpans: Array<{start: string, end: string, dates: string[], notes?: string}> = [];
        let currentSpan: {start: string, end: string, dates: string[], notes?: string} | null = null;

        sortedDates.forEach((dateStr, index) => {
          const currentDate = new Date(dateStr);
          const prevDate = index > 0 ? new Date(sortedDates[index - 1]) : null;

          // Check if this date is consecutive to the previous one
          const isConsecutive = prevDate &&
            (currentDate.getTime() - prevDate.getTime()) === (24 * 60 * 60 * 1000);

          if (!currentSpan || !isConsecutive) {
            // Start a new span
            if (currentSpan) {
              propertyBlockSpans.push(currentSpan);
            }

            // Get notes for the first date of the span
            const notes = blockedNotes[propertyId]?.[dateStr];
            currentSpan = {
              start: dateStr,
              end: dateStr,
              dates: [dateStr],
              notes: notes
            };
          } else {
            // Extend current span
            currentSpan.end = dateStr;
            currentSpan.dates.push(dateStr);
            
            // If current date has notes and span doesn't, add them
            if (!currentSpan.notes && blockedNotes[propertyId]?.[dateStr]) {
              currentSpan.notes = blockedNotes[propertyId][dateStr];
            }
          }
        });

        // Don't forget the last span
        if (currentSpan) {
          propertyBlockSpans.push(currentSpan);
        }

        // Store the spans for this property
        detectedBlockSpans[propertyId] = propertyBlockSpans;
      }
    });

    // Update state
    setBookings(transformedBookings);
    setRates(transformedRates);
    setBlockedNights(transformedBlockedNights);
    setBlockedNotes(blockedNotes);
    setBlockSpans(detectedBlockSpans);

  } catch (error) {
    console.error("Failed to fetch calendar data:", error);
    setError("Failed to load calendar data. Please try again.");
  } finally {
    setLoading(false);
  }
}, [listings, dateRange]);

  // Load more listings for infinite scroll
  const loadMoreListings = useCallback(() => {
    if (!hasMore) return;

    const currentDisplayed = displayedListings.length;
    const nextBatch = listings.slice(currentDisplayed, currentDisplayed + itemsPerLoad);

    if (nextBatch.length > 0) {
      setDisplayedListings(prev => [...prev, ...nextBatch]);
      setHasMore(currentDisplayed + nextBatch.length < listings.length);
    } else {
      setHasMore(false);
    }
  }, [displayedListings.length, listings, itemsPerLoad, hasMore]);



  // Handle search input changes
  const handleSearchChange = useCallback((value: string) => {
  setSearchUID(value);
}, []);

useEffect(() => {
  const handler = setTimeout(() => {
    loadListings();
  }, 400);

  return () => clearTimeout(handler);
}, [searchUID]);

  // Handle today button click
  const handleTodayClick = useCallback(() => {
    const today = new Date();
    setSelectedMonth(today.getMonth());
    setSelectedYear(today.getFullYear());
    setDateOffset(getCurrentWeekOffset(today.getMonth(), today.getFullYear()));
  }, []);

  // Infinite scroll handler for vertical scrolling
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    // Load more when user scrolls to bottom (with 100px threshold)
    if (scrollHeight - scrollTop <= clientHeight + 100 && hasMore && !loading) {
      loadMoreListings();
    }
  }, [hasMore, loading, loadMoreListings]);

  // Updated horizontal scroll handler for smooth single day navigation
  const handleHorizontalScroll = useCallback((e: React.WheelEvent<HTMLDivElement>) => {
    // Only handle horizontal scrolling or shift+wheel
    if (Math.abs(e.deltaX) > Math.abs(e.deltaY) || e.shiftKey) {
      e.preventDefault();

      if (isHorizontalScrolling) return; // Prevent multiple rapid scrolls

      setIsHorizontalScrolling(true);

      // Determine scroll direction - more sensitive for single day movement
      const scrollDirection = e.deltaX > 0 || (e.shiftKey && e.deltaY > 0) ? 'next' : 'prev';

      if (scrollDirection === 'next') {
        handleNextDay(); // Use day navigation for scrolling
      } else {
        handlePreviousDay(); // Use day navigation for scrolling
      }

      // Reset scrolling flag with shorter delay for smoother experience
      setTimeout(() => setIsHorizontalScrolling(false), 150);
    }
  }, [isHorizontalScrolling, handleNextDay, handlePreviousDay]);

  // Keyboard navigation handler
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.target !== e.currentTarget) return; // Only handle when calendar container is focused

    if (e.key === 'ArrowLeft') {
      e.preventDefault();
      handlePreviousDay(); // Use day navigation for keyboard
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      handleNextDay(); // Use day navigation for keyboard
    }
  }, [handleNextDay, handlePreviousDay]);

  // Simple debounce utility function
  function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  // Load listings when component mounts or when search changes
  useEffect(() => {
    loadListings();
  }, [loadListings]);

  // Load calendar data when listings change or date range changes
  useEffect(() => {
    if (listings.length > 0) {
      loadCalendarData();
    }
  }, [listings, loadCalendarData]);

  // Filter displayed listings based on search
  const filteredListings = displayedListings.filter(listing =>
    searchUID ? listing.name.toLowerCase().includes(searchUID.toLowerCase()) : true
  );

  // Update displayed listings when search changes
  React.useEffect(() => {
    if (listings.length > 0) {
      const filtered = listings.filter(listing =>
        searchUID ? listing.name.toLowerCase().includes(searchUID.toLowerCase()): true
      );

      // Reset displayed listings with first batch of filtered results
      setDisplayedListings(filtered.slice(0, itemsPerLoad));
      setHasMore(filtered.length > itemsPerLoad);
    }
  }, [listings, searchUID, itemsPerLoad]);

  return (
    <div className="h-full bg-white">
      <CalendarHeader
        selectedMonth={selectedMonth}
        selectedYear={selectedYear}
        listingsCount={listings.length}
      />

      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          className="mb-4"
        />
      )}

      <div className="border border-gray-300 rounded-lg shadow-sm overflow-hidden h-full">
        <DateNavigator
          visibleDates={visibleDates}
          onPrevious={handlePreviousWeek} // Use week navigation for arrow buttons
          onNext={handleNextWeek} // Use week navigation for arrow buttons
          listingColumnWidth={listingColumnWidth}
          onResizeStart={handleMouseDown}
          selectedMonth={selectedMonth}
          selectedYear={selectedYear}
          listingsCount={listings.length}
          searchUID={searchUID}
          onSearchChange={handleSearchChange}
          onMonthChange={setSelectedMonth}
          onYearChange={setSelectedYear}
          onTodayClick={handleTodayClick}
          navigationLoading={navigationLoading}
        />

        <div
          className="max-h-96 overflow-auto border-t-0 relative focus:outline-none"
          onScroll={handleScroll}
          onWheel={handleHorizontalScroll}
          onKeyDown={handleKeyDown}
          tabIndex={0}
          style={{
            overflowX: 'hidden',
            overflowY: 'auto',
            scrollBehavior: 'smooth'
          }}
        >
          {/* Navigation loading overlay */}
          {navigationLoading && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
              <div className="text-center">
                <Spin size="large" />
                <div className="text-gray-500 mt-2">Loading...</div>
              </div>
            </div>
          )}

          {loading && displayedListings.length === 0 && (
            <div className="text-center py-8">
              <Spin size="large" />
              <div className="text-gray-500 mt-2">Loading calendar data...</div>
            </div>
          )}

          {!loading && filteredListings.length === 0 && displayedListings.length === 0 && (
            <div className="text-center py-8">
              <div className="text-gray-500">
                {searchUID ? 'No listings found matching your search.' : 'No listings available for this period.'}
              </div>
            </div>
          )}

          {filteredListings.map((listing, index) => (
            <ListingRow
              key={listing.uid}
              listing={listing}
              visibleDates={dataDates}
              bookings={bookings}
              rates={rates[listing.uid] || {}}
              blockedNights={blockedNights[listing.uid] || []}
              blockedNotes={blockedNotes[listing.uid] || {}}
              blockSpans={blockSpans[listing.uid] || []}
              isLast={index === filteredListings.length - 1}
              listingColumnWidth={listingColumnWidth}
              onResizeStart={handleMouseDown}
            />
          ))}

          {/* Loading indicator for infinite scroll */}
          {hasMore && !loading && (
            <div className="text-center py-4">
              <div className="text-gray-500 text-sm">Scroll down to load more listings...</div>
            </div>
          )}

          {/* Loading spinner when loading more */}
          {loading && displayedListings.length > 0 && (
            <div className="text-center py-4">
              <Spin size="small" />
              <div className="text-gray-500 text-sm mt-2">Loading more listings...</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Calendar;