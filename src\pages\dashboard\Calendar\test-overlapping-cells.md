# Overlapping Cell System Test

## Test Data Structure

Based on the API data provided:

```json
{
  "properties": [
    {
      "id": 192,
      "listingid": "185313",
      "name": "Chic and Zen Apartment, Near Burj Khalifa Tower",
      "internal_listing_name": "3004-A8-1BR-THE RES-DOWTOW"
    }
  ],
  "calendar_data": {
    "185313": {
      "availability": {
        "2025-07-27": { "price": 322, "status": "available" },
        "2025-07-28": { "price": 282, "status": "blocked", "notes": "Maintenance—Fixem" },
        "2025-08-01": { "price": 267, "status": "available" },
        "2025-08-03": { "price": 345, "status": "reserved" },
        "2025-08-08": { "price": 363, "status": "available" }
      },
      "reservations": {
        "2025-08-03": {
          "guest_name": "<PERSON><PERSON>",
          "channel": "direct",
          "total_price": 386.43,
          "arrival_date": "2025-08-03",
          "departure_date": "2025-08-04"
        },
        "2025-08-04": {
          "guest_name": "<PERSON><PERSON>", 
          "channel": "bookingcom",
          "total_price": 1912.28,
          "arrival_date": "2025-08-04",
          "departure_date": "2025-08-08"
        }
      }
    }
  }
}
```

## Expected Overlapping Cell Behavior

### 2025-08-03 (Check-in for first reservation)
- **Right side (50% width)**: Check-in cell for "Maita Szrebrodolszki" (direct channel)
- **Cell position**: 50% from left, 50% width
- **Clip path**: Slanted start for multi-day reservation
- **Background**: Green for direct channel

### 2025-08-04 (Check-out + Check-in on same day)
- **Left side (50% width)**: Check-out cell for first reservation (direct)
- **Right side (50% width)**: Check-in cell for second reservation (bookingcom)
- **Gap**: 10% between the two cells
- **Clip paths**: 
  - Left: Slanted end for check-out
  - Right: Slanted start for check-in

### 2025-08-05, 2025-08-06, 2025-08-07 (Ongoing reservation)
- **Full width**: Ongoing reservation cell for "Maita Szrebrodolszki" (bookingcom)
- **Clip path**: Straight rectangle (no slant)
- **Background**: Blue for booking.com channel

### 2025-08-08 (Check-out + Availability)
- **Left side (50% width)**: Check-out cell for reservation
- **Right side (50% width)**: Availability cell showing rate AED 363
- **Gap**: 10% between check-out and availability

### 2025-07-28 (Blocked date)
- **Full width**: Blocked cell with maintenance note
- **Background**: Gray
- **Icon**: Lock icon
- **Text**: "Maintenance—Fixem"

## Key Features Implemented

1. **50% Width Overlapping**: Each element takes 50% width but covers 40% of column
2. **Slanted Tapes**: Multi-day reservations have slanted start/end
3. **10% Gaps**: Proper spacing between different elements
4. **Priority System**: Check-outs (left) → Check-ins (right) → Ongoing → Blocks → Availability
5. **Channel Colors**: Different background colors for different booking channels
6. **Proper Text**: Guest names, amounts, and availability rates displayed correctly

## Testing Checklist

- [ ] Check-in cells appear on right side with slanted start
- [ ] Check-out cells appear on left side with slanted end  
- [ ] Ongoing reservations span full width with straight edges
- [ ] Blocked dates show lock icon and notes
- [ ] Availability shows rates and minimum nights
- [ ] Proper gaps between overlapping elements
- [ ] Channel-specific colors are applied
- [ ] Text is readable and properly positioned
- [ ] No missing cells or irregular gaps
- [ ] Smooth transitions between different cell types
