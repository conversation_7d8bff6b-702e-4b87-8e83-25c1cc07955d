import React from "react";
import { Booking, Rate, Block } from "./types";
import airbnbIcon from "../../../assets/channelIcons/airbnb.png";
import bookingIcon from "../../../assets/channelIcons/booking_logo_icon_169472.png";
import expediaIcon from "../../../assets/channelIcons/expedia.png"
import homeawayIcon from "../../../assets/channelIcons/HomeAway.png"
import { LockOutlined } from "@ant-design/icons";
import { Tooltip } from "antd";

// Enhanced interface for individual date analysis
interface IndividualDateAnalysis {
  date: Date;
  dateKey: string;
  index: number;
  allBookings: Booking[];
  blocks: Block[];
  rate?: Rate;
  // NEW: Add specific booking analysis for this date
  hasCheckIn: boolean;
  hasCheckOut: boolean;
  checkInBooking?: Booking;
  checkOutBooking?: Booking;
}

interface CalendarCellProps {
  date: Date;
  booking?: Booking;
  bookings?: Booking[];
  blocks?: Block[];
  rate?: Rate;
  isBlocked?: boolean;
  isGrouped?: boolean;
  groupSpan?: number;
  groupDates?: Date[];
  groupData?: Booking | Block;
  individualDateAnalysis?: IndividualDateAnalysis[];
  notes?: string;
  isBookingStart?: boolean;
  isBookingEnd?: boolean;
  showBookingText?: boolean;
  cellIndex?: number;
  propertyName?: string;
  // NEW: Add previous date rate for availability extension
  previousDateRate?: Rate;
  // NEW: Add flag to indicate if previous date had availability
  hasPreviousAvailability?: boolean;
}

interface DaySlantStatus {
  hasCheckIn: boolean;
  hasCheckOut: boolean;
  checkInData: Booking | Block | null;
  checkOutData: Booking | Block | null;
  availabilityData: Rate | null;
  hasBlockStart: boolean;
  hasBlockEnd: boolean;
  blockStartData: Block | null;
  blockEndData: Block | null;
  // NEW: Add previous date availability
  hasPreviousAvailability: boolean;
  previousAvailabilityData: Rate | null;
}

interface SlantLayer {
  type: 'checkout' | 'checkin' | 'blockEnd' | 'blockStart' | 'availability' | 'previousAvailability';
  data: Booking | Block | Rate;
  styles: React.CSSProperties;
  content: JSX.Element;
  zIndex: number;
}

const CalendarCell: React.FC<CalendarCellProps> = ({
  date,
  booking,
  bookings = [],
  blocks = [],
  rate,
  isBlocked = false,
  isGrouped = false,
  groupSpan = 1,
  groupDates = [],
  groupData,
  individualDateAnalysis = [],
  notes,
  isBookingStart = false,
  isBookingEnd = false,
  showBookingText = false,
  cellIndex = 0,
  propertyName = '',
  previousDateRate,
  hasPreviousAvailability = false,
}) => {
 
  const checkSameDayOrConsecutiveEvents = (slantStatus: DaySlantStatus, groupDates: Date[] = []): boolean => {
  const currentDateStr = formatDateKey(date);
  
  // Helper function to check if two dates are consecutive
  const areConsecutiveDays = (date1: string, date2: string): boolean => {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    const timeDiff = Math.abs(d2.getTime() - d1.getTime());
    const dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
    return dayDiff === 1;
  };
  
  // Helper function to check if dates are within visible 7-day range
  const areWithinVisibleRange = (date1: string, date2: string, visibleDates: Date[]): boolean => {
    if (visibleDates.length === 0) return false;
    
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    
    const isDate1Visible = visibleDates.some(vDate => formatDateKey(vDate) === date1);
    const isDate2Visible = visibleDates.some(vDate => formatDateKey(vDate) === date2);
    
    return isDate1Visible && isDate2Visible;
  };
  
  // Check if block start and end are on the same day
  if (slantStatus.hasBlockStart && slantStatus.hasBlockEnd) {
    const blockStartDate = slantStatus.blockStartData ? normalizeDateFormat(slantStatus.blockStartData.start_date) : null;
    const blockEndDate = slantStatus.blockEndData ? normalizeDateFormat(slantStatus.blockEndData.end_date) : null;
    
    if (blockStartDate && blockEndDate) {
      // Same day
      if (blockStartDate === blockEndDate) {
        return true;
      }
      
    }
  }
  
  // Check if checkin and checkout are on the same day or consecutive days
  if (slantStatus.hasCheckIn && slantStatus.hasCheckOut) {
    const checkinDate = isBooking(slantStatus.checkInData) ? 
      normalizeDateFormat(slantStatus.checkInData.arrival_date) : null;
    const checkoutDate = isBooking(slantStatus.checkOutData) ? 
      normalizeDateFormat(slantStatus.checkOutData.departure_date) : null;
    
    if (checkinDate && checkoutDate) {
      
      if (areConsecutiveDays(checkinDate, checkoutDate) && 
          areWithinVisibleRange(checkinDate, checkoutDate, groupDates)) {
        return true;
      }
    }
  }
  
  return false;
};
  // const logBlockAnalysis = (date: Date, blocks: Block[], propertyName: string) => {
  //   const dateStr = formatDateKey(date);
  //   const shouldLog = true; // Set to false to disable
    
  //   if (shouldLog && blocks.length > 0) {
  //     console.group(`🔒 BLOCK ANALYSIS - ${dateStr} - ${propertyName}`);
  //     console.log('Total blocks found:', blocks.length);
      
  //     blocks.forEach((block, index) => {
  //       console.log(`Block ${index + 1}:`, {
  //         start_date: block.start_date,
  //         end_date: block.end_date,
  //         notes: block.notes,
  //         normalized_start: normalizeDateFormat(block.start_date),
  //         normalized_end: normalizeDateFormat(block.end_date),
  //         is_start_date: normalizeDateFormat(block.start_date) === dateStr,
  //         is_end_date: normalizeDateFormat(block.end_date) === dateStr
  //       });
  //     });
  //     console.groupEnd();
  //   }
  // };

  // const logSlantStatusAnalysis = (date: Date, slantStatus: DaySlantStatus, propertyName: string) => {
  //   const dateStr = formatDateKey(date);
  //   const shouldLog = true;
    
  //   if (shouldLog && (slantStatus.hasBlockStart || slantStatus.hasBlockEnd || slantStatus.hasCheckIn || slantStatus.hasCheckOut)) {
  //     console.group(`📊 SLANT STATUS ANALYSIS - ${dateStr} - ${propertyName}`);
  //     console.log('Slant Status Summary:', {
  //       hasCheckIn: slantStatus.hasCheckIn,
  //       hasCheckOut: slantStatus.hasCheckOut,
  //       hasBlockStart: slantStatus.hasBlockStart,
  //       hasBlockEnd: slantStatus.hasBlockEnd,
  //       hasPreviousAvailability: slantStatus.hasPreviousAvailability
  //     });
      
  //     if (slantStatus.hasBlockStart && slantStatus.blockStartData) {
  //       console.log('Block Start Data:', slantStatus.blockStartData);
  //     }
      
  //     if (slantStatus.hasBlockEnd && slantStatus.blockEndData) {
  //       console.log('Block End Data:', slantStatus.blockEndData);
  //     }
      
  //     console.groupEnd();
  //   }
  // };

  // const logLayerRender = (layerType: string, styles: React.CSSProperties, data: any, date: Date) => {
  //   const dateStr = formatDateKey(date);
  //   const shouldLog = true;
    
  //   if (shouldLog) {
  //     console.log(`🎨 RENDERING LAYER - ${layerType.toUpperCase()} - ${dateStr}`, {
  //       cellIndex:{cellIndex},
  //       styles: {
  //         clipPath: styles.clipPath,
  //         left: styles.left,
  //         width: styles.width,
  //         zIndex: styles.zIndex,
  //         backgroundColor: styles.backgroundColor
  //       },
  //       data: layerType.includes('block') ? {
  //         notes: data?.notes,
  //         start_date: data?.start_date,
  //         end_date: data?.end_date
  //       } : data
  //     });
  //   }
  // };

  // const logMultipleRenderCheck = (date: Date, layers: SlantLayer[], propertyName: string) => {
  //   const dateStr = formatDateKey(date);
  //   const shouldLog = true;
    
  //   if (shouldLog && layers.length > 1) {
  //     console.group(`⚠️ MULTIPLE LAYERS DETECTED - ${dateStr} - ${propertyName}`);
  //     console.log(`Total layers: ${layers.length}`);
      
  //     layers.forEach((layer, index) => {
  //       console.log(`Layer ${index + 1}:`, {
  //         type: layer.type,
  //         zIndex: layer.zIndex,
  //         clipPath: layer.styles.clipPath,
  //         left: layer.styles.left,
  //         width: layer.styles.width
  //       });
  //     });
      
  //     // Check for potential conflicts
  //     const blockLayers = layers.filter(l => l.type.includes('block'));
  //     const bookingLayers = layers.filter(l => ['checkin', 'checkout'].includes(l.type));
      
  //     if (blockLayers.length > 0 && bookingLayers.length > 0) {
  //       console.warn('❌ POTENTIAL CONFLICT: Both block and booking layers present');
  //     }
      
  //     console.groupEnd();
  //   }
  // };
  const formatDateKey = (date: Date): string => {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
  };

  const normalizeDateFormat = (dateStr: string | undefined): string | undefined => {
    if (!dateStr) return undefined;

    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      return dateStr;
    }

    if (/^\d{2}-\d{2}-\d{4}$/.test(dateStr)) {
      const [day, month, year] = dateStr.split('-');
      return `${year}-${month}-${day}`;
    }

    try {
      const parsedDate = new Date(dateStr);
      if (!isNaN(parsedDate.getTime())) {
        return formatDateKey(parsedDate);
      }
    } catch (error) {
      console.warn(`Failed to parse date: ${dateStr}`);
    }

    return dateStr;
  };

  const truncateText = (text: string, maxLength: number = 25): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const isBooking = (data: any): data is Booking => {
    return data && 'guestName' in data && 'channel' in data;
  };

  const isBlock = (data: any): data is Block => {
    return data && ('start_date' in data || 'end_date' in data || 'notes' in data);
  };

  // Enhanced booking-specific slant analysis with block events and previous availability
  const getEnhancedSlantStatusForDate = (targetDate: Date): DaySlantStatus => {
    const targetDateKey = formatDateKey(targetDate);
    // logBlockAnalysis(targetDate, blocks || [], propertyName || '');
    
    // First, try to use individual date analysis if available
    if (individualDateAnalysis.length > 0) {
      const dateAnalysis = individualDateAnalysis.find(analysis => analysis.dateKey === targetDateKey);
      if (dateAnalysis) {
        // Analyze blocks for start/end
        const blockStart = dateAnalysis.blocks.find(block => {
          const normalizedBlockStart = normalizeDateFormat(block.start_date);
          return normalizedBlockStart === targetDateKey;
        });
        
        const blockEnd = dateAnalysis.blocks.find(block => {
          const normalizedBlockEnd = normalizeDateFormat(block.end_date);
          return normalizedBlockEnd === targetDateKey;
        });
        // logSlantStatusAnalysis(targetDate, {
        //   hasCheckIn: dateAnalysis.hasCheckIn,
        //   hasCheckOut: dateAnalysis.hasCheckOut,
        //   checkInData: dateAnalysis.checkInBooking || null,
        //   checkOutData: dateAnalysis.checkOutBooking || null,
        //   availabilityData: dateAnalysis.rate || rate || null,
        //   hasBlockStart: !!blockStart,
        //   hasBlockEnd: !!blockEnd,
        //   blockStartData: blockStart || null,
        //   blockEndData: blockEnd || null,
        //   hasPreviousAvailability,
        //   previousAvailabilityData: previousDateRate || null
        // }, propertyName || '');
        return {
          hasCheckIn: dateAnalysis.hasCheckIn,
          hasCheckOut: dateAnalysis.hasCheckOut,
          checkInData: dateAnalysis.checkInBooking || null,
          checkOutData: dateAnalysis.checkOutBooking || null,
          availabilityData: dateAnalysis.rate || rate || null,
          hasBlockStart: !!blockStart,
          hasBlockEnd: !!blockEnd,
          blockStartData: blockStart || null,
          blockEndData: blockEnd || null,
          hasPreviousAvailability,
          previousAvailabilityData: previousDateRate || null
        };
      }
    }

    // Fallback: Analyze using group data and bookings
    return getEnhancedDaySlantStatus(targetDate, bookings, blocks, propertyName);
  };

  // Enhanced day slant status logic with block start/end detection and previous availability
  const getEnhancedDaySlantStatus = (dayDate: Date, dayBookings: Booking[], dayBlocks: Block[], propName: string = ''): DaySlantStatus => {
    const dateStr = formatDateKey(dayDate);
    const currentDay = dayDate.getDate();
    const shouldLog = currentDay >= 1 && currentDay <= 7;
    const targetProperties = ['401-A40-3BR-SURF-CREHAR', '2504-A25-1BR-CONC -JLT'];
    const isTargetProperty = targetProperties.includes(propName);

    let hasCheckIn = false;
    let hasCheckOut = false;
    let checkInData: Booking | Block | null = null;
    let checkOutData: Booking | Block | null = null;
    let hasBlockStart = false;
    let hasBlockEnd = false;
    let blockStartData: Block | null = null;
    let blockEndData: Block | null = null;

    // For grouped bookings, only check if THIS specific date is a check-in/check-out
    if (groupData && isGrouped && isBooking(groupData)) {
      const normalizedGroupArrival = normalizeDateFormat(groupData.arrival_date);
      const normalizedGroupDeparture = normalizeDateFormat(groupData.departure_date);

      // Only mark as check-in if THIS date is the arrival date
      if (normalizedGroupArrival === dateStr) {
        hasCheckIn = true;
        checkInData = groupData;
      }

      // Only mark as check-out if THIS date is the departure date
      if (normalizedGroupDeparture === dateStr) {
        hasCheckOut = true;
        checkOutData = groupData;
      }
    }

    // Check individual bookings (for non-grouped or additional bookings)
    const processedBookings = new Set<string>();
    dayBookings.forEach(booking => {
      const bookingKey = `${booking.guestName}-${booking.channel}-${booking.arrival_date}-${booking.departure_date}`;

      if (processedBookings.has(bookingKey)) return;
      processedBookings.add(bookingKey);

      const normalizedArrivalDate = normalizeDateFormat(booking.arrival_date);
      const normalizedDepartureDate = normalizeDateFormat(booking.departure_date);

      if (normalizedArrivalDate === dateStr) {
        hasCheckIn = true;
        checkInData = booking;
      }

      if (normalizedDepartureDate === dateStr) {
        hasCheckOut = true;
        checkOutData = booking;
      }
    });

    // Check blocks for both start and end
    dayBlocks.forEach((block) => {
      if (block) {
        const normalizedBlockStart = normalizeDateFormat(block.start_date);
        const normalizedBlockEnd = normalizeDateFormat(block.end_date);

        if (normalizedBlockStart === dateStr) {
          hasBlockStart = true;
          blockStartData = block;
        }
        if (normalizedBlockEnd === dateStr) {
          hasBlockEnd = true;
          blockEndData = block;
        }
      }
    });

    const result = {
      hasCheckIn,
      hasCheckOut,
      checkInData,
      checkOutData,
      availabilityData: rate || null,
      hasBlockStart,
      hasBlockEnd,
      blockStartData,
      blockEndData,
      hasPreviousAvailability,
      previousAvailabilityData: previousDateRate || null
    };

    if (shouldLog && isTargetProperty && (hasCheckIn || hasCheckOut || hasBlockStart || hasBlockEnd)) {
    }

    return result;
  };

  // Enhanced utility function to analyze slant status for ALL dates in a group
  const getGroupSlantStatus = (groupDates: Date[], dayBookings: Booking[], dayBlocks: Block[], propName: string = ''): DaySlantStatus => {
    let hasCheckIn = false;
    let hasCheckOut = false;
    let checkInData: Booking | Block | null = null;
    let checkOutData: Booking | Block | null = null;
    let hasBlockStart = false;
    let hasBlockEnd = false;
    let blockStartData: Block | null = null;
    let blockEndData: Block | null = null;

    // Check each date in the group for check-in/check-out/block status
    groupDates.forEach((groupDate) => {
      // Get slant status for this specific date
      const dateSlantStatus = getEnhancedDaySlantStatus(groupDate, dayBookings, dayBlocks, propName);

      // Accumulate check-in/check-out/block status across all dates in the group
      if (dateSlantStatus.hasCheckIn) {
        hasCheckIn = true;
        checkInData = dateSlantStatus.checkInData;
      }

      if (dateSlantStatus.hasCheckOut) {
        hasCheckOut = true;
        checkOutData = dateSlantStatus.checkOutData;
      }
      
      if (dateSlantStatus.hasBlockStart) {
        hasBlockStart = true;
        blockStartData = dateSlantStatus.blockStartData;
      }
      
      if (dateSlantStatus.hasBlockEnd) {
        hasBlockEnd = true;
        blockEndData = dateSlantStatus.blockEndData;
      }
    });

    return {
      hasCheckIn,
      hasCheckOut,
      checkInData,
      checkOutData,
      availabilityData: rate || null,
      hasBlockStart,
      hasBlockEnd,
      blockStartData,
      blockEndData,
      hasPreviousAvailability,
      previousAvailabilityData: previousDateRate || null
    };
  };

  const getChannelIcon = (channel: string): JSX.Element => {
    const channelLower = channel.toLowerCase();

    if (channelLower.includes('airbnb')) {
      return <img src={airbnbIcon} alt="Airbnb" className="w-6 h-6" />;
    }

    if (channelLower.includes('booking')) {
      return <img src={bookingIcon} alt="Booking.com" className="w-5 h-5" />;
    }

    if (channelLower.includes('expedia')){
      return <img src={expediaIcon} alt="expedia" className="w-5 h-5" />
    }
    if (channelLower.includes('homeaway')){
      return <img src={homeawayIcon} alt="homeaway" className="w-5 h-5"/>
    }

    const defaultIcons: { [key: string]: string } = {
      'vrbo': '🏡',
      'direct': '📞'
    };

    return <span className="text-xl">{defaultIcons[channelLower] || '📅'}</span>;
  };

  const getReservationColor = (): string => {
    return 'text-white';
  };

  const getReservationStyle = (): React.CSSProperties => {
    return {
      backgroundColor: '#41645c'
    };
  };

  // Generate multiple slant layers based on status
  const generateSlantLayers = (slantStatus: DaySlantStatus): SlantLayer[] => {
    const layers: SlantLayer[] = [];

    
    // Base styles for different slant types
    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      top: '50%',
      transform: 'translateY(-50%)',
      height: '46px',
      overflow: 'visible',
    };
    const shouldMergeBlockEvents = checkSameDayOrConsecutiveEvents(slantStatus, groupDates) && 
                                 slantStatus.hasBlockStart && slantStatus.hasBlockEnd;
    const shouldMergeBookingEvents = checkSameDayOrConsecutiveEvents(slantStatus, groupDates) && 
                                   slantStatus.hasCheckIn && slantStatus.hasCheckOut;

    // 0. Previous date availability extension layer - NEW
    if (slantStatus.hasPreviousAvailability && slantStatus.previousAvailabilityData && !isBlocked && !slantStatus.hasCheckOut && !slantStatus.hasBlockEnd && cellIndex === 0) {
      
      const previousAvailabilityStyles: React.CSSProperties = {
        ...baseStyle,
        clipPath: 'polygon(0% 0%, 96% 0%, 79% 100%, 0% 100%)',
        left:' -25%',
        width: '67%',
        paddingLeft: '12%',
        paddingRight: '8%',
        border: 'none',
        borderRadius:'4px',
        margin: '0px',
        boxShadow: 'none',
        zIndex: 0,
      };
      // logLayerRender('previousAvailability', previousAvailabilityStyles, slantStatus.previousAvailabilityData, date);
      layers.push({
        type: 'previousAvailability',
        data: slantStatus.previousAvailabilityData,
        styles: previousAvailabilityStyles,
        zIndex: 0,
        content: (
          <div className="bg-gray-100 flex flex-col justify-center items-center text-center" style={{
        zIndex: 0,
          position: 'absolute',
          top: '50%',
          transform: 'translateY(-50%)',
          height: '46px',
          overflow: 'visible',
          left: '33%',
          }}>
            <div className="text-xs text-gray-700 font-medium">
              AED {slantStatus.previousAvailabilityData.rate || 0}
            </div>
            <div className="text-xs text-gray-500">
              {slantStatus.previousAvailabilityData.minNights || 0} nights min
            </div>
          </div>
        )
      });
    }

    // 1. Availability layer - always show if rate exists and no booking occupies the cell
    if (slantStatus.availabilityData && !booking && !isBlocked) {
      
      const availabilityStyles: React.CSSProperties = {
        ...baseStyle,
        clipPath: 'polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%)',
        left: '37%',
        width: '109%',
        paddingLeft: '12%',
        paddingRight: '12%',
        zIndex: 1,
        // backgroundColor: '#f3f4f6',
      };
      // logLayerRender('availability', availabilityStyles, slantStatus.availabilityData, date);
      layers.push({
        type: 'availability',
        data: slantStatus.availabilityData,
        styles: availabilityStyles,
        zIndex: 1,
        content: (
          <div className="flex flex-col justify-center items-center text-center">
            <div className="text-xs text-gray-700 font-medium">
              AED {slantStatus.availabilityData.rate || 0}
            </div>
            <div className="text-xs text-gray-500">
              {slantStatus.availabilityData.minNights || 0} nights min
            </div>
          </div>
        )
      });
    }

// Handle BOTH Check-in and Check-out in the same cell
const hasBothCheckInAndOut = slantStatus.hasCheckIn && slantStatus.hasCheckOut;

let checkinStyles: React.CSSProperties | null = null;
let checkoutStyles: React.CSSProperties | null = null;
    // Check if checkout layer should really be rendered
    const shouldRenderCheckout = (() => {
      if (!slantStatus.hasCheckOut || !slantStatus.checkOutData) return false;

      const checkoutDateStr = normalizeDateFormat(
        isBooking(slantStatus.checkOutData)
          ? slantStatus.checkOutData.departure_date
          : isBlock(slantStatus.checkOutData)
            ? slantStatus.checkOutData.end_date
            : ''
      );

        const currentDateStr = formatDateKey(date);

        // Only render checkout layer if the current date is the checkout date
        return checkoutDateStr === currentDateStr;
      })();

  if (hasBothCheckInAndOut) {
    checkoutStyles = {
      ...baseStyle,
      clipPath: 'polygon(0% 0%, 48% 0%, 43% 100%, 0% 100%)',
      left: cellIndex===0?'-5%':'-10%',
      width: cellIndex===0?'25%':'46%',
      paddingLeft:cellIndex===0?'5%':'6%',
      paddingRight: '4%',
      zIndex: 3,
      ...getReservationStyle(),
    };

    checkinStyles = {
      ...baseStyle,
      clipPath: cellIndex==0?'polygon(2% 0%, 100% 0%, 100% 100%, 1% 100%)':'polygon(2% 0%, 100% 0%, 100% 100%, 0% 100%)',
      left: cellIndex==0?'6%':'12%',
      width: '109%',
      paddingLeft: '6%',
      paddingRight: '4%',
      zIndex: 2,
      ...getReservationStyle(),
    };
  } else {
    
    // Fallback if only one exists
    if (slantStatus.hasCheckOut && slantStatus.checkOutData) {
      
      const isFromIndividualAnalysis = individualDateAnalysis.length > 0 &&
        individualDateAnalysis.some(analysis =>
          analysis.dateKey === formatDateKey(date) && analysis.hasCheckOut
        );

      checkoutStyles = {
        ...baseStyle,
        clipPath: isFromIndividualAnalysis
          ? 'polygon(0% 0%, 96% 0%, 80% 100%, 0% 100%)'
          : 'polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%)',
        left: isFromIndividualAnalysis ? '-25%' : '0%',
        width: isFromIndividualAnalysis ? '68%' : '46%',
        paddingLeft: isFromIndividualAnalysis ? '30%' : '12%',
        paddingRight: '8%',
        zIndex: 3,
        ...getReservationStyle(),
      };
    }
    if (slantStatus.hasCheckIn && slantStatus.checkInData) {
    checkinStyles = {
      ...baseStyle,
      clipPath: 'polygon(2% 0%, 97% 0%, 94% 100%, 0% 100%)',
      left: '10%',
      width: '109%',
      paddingLeft: '12%',
      paddingRight: '8%',
      zIndex: 2,
      ...getReservationStyle(),
    };
  }
}

// Add CHECK-OUT layer if style is defined
if (checkoutStyles && slantStatus.checkOutData) {

  const checkoutBooking = isBooking(slantStatus.checkOutData) ? slantStatus.checkOutData : null;
  if (checkoutBooking && showBookingText) {
  }
  // logLayerRender('checkout', checkoutStyles, slantStatus.checkOutData, date);
  layers.push({
    type: 'checkout',
    data: slantStatus.checkOutData,
    styles: checkoutStyles,
    zIndex: 3,
    content: (
      <div className="absolute flex flex-col justify-center">
        {checkoutBooking && showBookingText && cellIndex===0 && (
          <>
            <div className="flex items-center gap-1">
              {getChannelIcon(checkoutBooking.channel)}
              <span className="truncate text-xs text-white font-medium">
                {checkoutBooking.guestName}
              </span>
            </div>
            <div className="text-xs font-medium text-white">
              AED {checkoutBooking.amount}
            </div>
          </>
        )}
      </div>
    )
  });

}

// Add CHECK-IN layer if style is defined
if (checkinStyles && slantStatus.checkInData) {
  
  const checkinBooking = isBooking(slantStatus.checkInData) ? slantStatus.checkInData : null;
  // logLayerRender('checkin', checkinStyles, slantStatus.checkInData, date);
  layers.push({
    type: 'checkin',
    data: slantStatus.checkInData,
    styles: checkinStyles,
    zIndex: 2,
    content: (
      <div className="absolute flex flex-col justify-center">
        {checkinBooking && showBookingText && (
          <>
            <div className="flex items-center gap-1">
              {getChannelIcon(checkinBooking.channel)}
              <span className="truncate text-xs text-white font-medium">
                {checkinBooking.guestName}
              </span>
            </div>
            <div className="text-xs font-medium text-white">
              AED {checkinBooking.amount}
            </div>
          </>
        )}
      </div>
    )
  });
}
  
  if (shouldMergeBlockEvents) {
    // Render merged block layer
    const mergedBlockData = slantStatus.blockStartData || slantStatus.blockEndData;
    const mergedBlockStyles: React.CSSProperties = {
      clipPath: 'polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%)',
      left: '37%',
      width: '109%',
      paddingLeft: '12%',
      paddingRight: '12%',
      border: 'none',
      borderRadius: '4px',
      margin: '0px',
      position: 'absolute',
      top: '50%',
      transform: 'translateY(-50%)',
      height: '46px',
      boxShadow: 'none',
      zIndex: 4,
      backgroundColor: '#d1d5db'
    };
    
    layers.push({
      type: 'blockStart',
      data: mergedBlockData!,
      styles: mergedBlockStyles,
      zIndex: 4,
      content: (
        <div className="flex flex-col justify-center items-center text-center">
          <LockOutlined className="text-gray-600 text-sm mb-1" />
          {mergedBlockData && mergedBlockData.notes && cellIndex === 0 && (
            <div className="text-xs text-gray-500">
              {truncateText(mergedBlockData.notes, 15)}
            </div>
          )}
        </div>
      )
    });
  } else {

    // 4. Block End layer - should appear when block ends and allow availability
    if (slantStatus.hasBlockEnd && slantStatus.blockEndData) {
      const blockEndStyles: React.CSSProperties = {
        ...baseStyle,
        clipPath: 'polygon(0% 0%, 100% 0%, 93% 100%, 0% 100%)',
        left: '0%',
        width: '135%',
        paddingLeft: '12%',
        paddingRight: '8%',
        zIndex: 4,
        backgroundColor: '#d1d5db',
      };
      // logLayerRender('blockEnd', blockEndStyles, slantStatus.blockEndData, date);
      layers.push({
        type: 'blockEnd',
        data: slantStatus.blockEndData,
        styles: blockEndStyles,
        zIndex: 4,
        content: (
          <div className="flex flex-col justify-center items-start">
            <LockOutlined className="text-gray-600 text-sm mb-1" />
            {slantStatus.blockEndData.notes && cellIndex===0 && (
              <div className="text-xs text-gray-500 text-left w-full">
                {truncateText(slantStatus.blockEndData.notes, 15)}
              </div>
            )}
          </div>
        )
      });
    }

    // 5. Block Start layer - should appear when block starts
    if (slantStatus.hasBlockStart && slantStatus.blockStartData) {
      const blockStartStyles: React.CSSProperties = {
        ...baseStyle,
        clipPath: 'polygon(3% 0%, 97% 0%, 94% 100%, 0% 100%)',
        left: '5%',
        width: '109%',
        paddingLeft: '15%',
        paddingRight: '8%',
        zIndex: 4,
        backgroundColor: '#d1d5db',
      };
      // logLayerRender('blockStart', blockStartStyles, slantStatus.blockStartData, date);
      layers.push({
        type: 'blockStart',
        data: slantStatus.blockStartData,
        styles: blockStartStyles,
        zIndex: 4,
        content: (
          <div className="flex flex-col justify-center items-start">
            <LockOutlined className="text-gray-600 text-sm mb-1" />
            {slantStatus.blockStartData.notes && (
              <div className="text-xs text-gray-500 text-left w-full">
                {truncateText(slantStatus.blockStartData.notes, 15)}
              </div>
            )}
          </div>
        )
      });
    }
  }

    // 6. Special case: If checkout and no other booking/block events, create availability layer
    if (slantStatus.hasCheckOut && !slantStatus.hasCheckIn && !slantStatus.hasBlockStart && !booking && !isBlocked) {
      
      const availabilityRate = slantStatus.availabilityData || { rate: 0, minNights: 0 };
      const availabilityStyles: React.CSSProperties = {
        ...baseStyle,
        clipPath: 'polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%)',
        left: '37%',
        width: '109%',
        paddingLeft: '12%',
        paddingRight: '12%',
        backgroundColor: '#f3f4f6',
        zIndex: 1,
      };

      layers.push({
        type: 'availability',
        data: availabilityRate as Rate,
        styles: availabilityStyles,
        zIndex: 1,
        content: (
          <div className="flex flex-col justify-center items-center text-center">
            <div className="text-xs text-gray-700 font-medium">
              AED {availabilityRate.rate || 0}
            </div>
            <div className="text-xs text-gray-500">
              {availabilityRate.minNights || 0} nights min
            </div>
          </div>
        )
      });
    }
    // 7. Special case: If block end and no other events, create availability layer
    if (slantStatus.hasBlockEnd && !slantStatus.hasBlockStart && !slantStatus.hasCheckIn && !booking && !isBlocked) {

      const availabilityRate = slantStatus.availabilityData || { rate: 0, minNights: 0 };
      const availabilityStyles: React.CSSProperties = {
        ...baseStyle,
        clipPath: 'polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%)',
        left: '37%',
        width: '109%',
        paddingLeft: '12%',
        paddingRight: '12%',
        backgroundColor: '#f3f4f6',
        zIndex: 1,
      };

      layers.push({
        type: 'availability',
        data: availabilityRate as Rate,
        styles: availabilityStyles,
        zIndex: 1,
        content: (
          <div className="flex flex-col justify-center items-center text-center">
            <div className="text-xs text-gray-700 font-medium">
              AED {availabilityRate.rate || 0}
            </div>
            <div className="text-xs text-gray-500">
              {availabilityRate.minNights || 0} nights min
            </div>
          </div>
        )
      });
    }   
    
    // logMultipleRenderCheck(date, layers, propertyName || '');
    return layers.sort((a, b) => a.zIndex - b.zIndex);
  };

  // Legacy single slant styles for backwards compatibility when only one event occurs
  const getLegacySlantingStyles = (slantStatus: DaySlantStatus): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      top: '50%',
      transform: 'translateY(-50%)',
      height: '46px',
      zIndex: 2,
      overflow: 'visible',
    };


    if (slantStatus.hasCheckIn && slantStatus.hasCheckOut) {
      return {
        ...baseStyle,
        clipPath: 'polygon(2% 0%, 97% 0%, 94% 100%, 0% 100%)',
        left: '9%',
        width: '100%',
        paddingLeft: '12%',
        paddingRight: '8%',
      };
    } else if (slantStatus.hasCheckIn) {
      return {
        ...baseStyle,
        clipPath: 'polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%)',
        left: '33%',
        width: '72%',
        paddingLeft: '12%',
        paddingRight: '8%',
      };
    } else if (slantStatus.hasCheckOut) {
      const isFromIndividualAnalysis = individualDateAnalysis.length > 0 &&
        individualDateAnalysis.some(analysis =>
          analysis.dateKey === formatDateKey(date) && analysis.hasCheckOut
        );

      if (isFromIndividualAnalysis) {
        return {
          ...baseStyle,
          clipPath: 'polygon(0% 0%, 96% 0%, 80% 100%, 0% 100%)',
          left: '-25%',
          width: '68%',
          paddingLeft: '30%',
          paddingRight: '8%',
          overflow: 'visible',
          zIndex: 3,
        };
      } else {
        return {
          ...baseStyle,
          clipPath: 'polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%)',
          left: '0%',
          width: '46%',
          paddingLeft: '12%',
          paddingRight: '8%',
          zIndex: 3,
        };
      }
    }

    return {
      ...baseStyle,
      left: '0%',
      width: '100%',
      padding: '8px',
    };
  };

  if (isBlocked) {
    // For grouped cells, analyze all dates in the group; for individual cells, analyze just the single date
    const daySlantStatus = isGrouped && groupDates.length > 1
      ? getGroupSlantStatus(groupDates, bookings || [], blocks || [], propertyName)
      : getEnhancedSlantStatusForDate(date);
    // Generate multiple slant layers for blocked cells
    const slantLayers = generateSlantLayers(daySlantStatus);
//     const logRenderSummary = (date: Date, layerCount: number, isBlocked: boolean, hasBooking: boolean, propertyName: string) => {
//   const dateStr = formatDateKey(date);
//   const shouldLog = true;
  
//   if (shouldLog) {
//     console.log(`📋 RENDER SUMMARY - ${dateStr} - ${propertyName}`, {
//       totalLayers: layerCount,
//       isBlocked,
//       hasBooking,
//       cellIndex,
//       renderType: layerCount > 1 ? 'MULTIPLE_LAYERS' : layerCount === 1 ? 'SINGLE_LAYER' : 'AVAILABILITY_ONLY'
//     });
//   }
// };
// logRenderSummary(date, slantLayers.length, true, !!booking, propertyName || '');
    return (
      <div className="h-16 border-r border-gray-300 last:border-r-0 p-1 bg-white hover:bg-gray-50 transition-colors duration-150 relative overflow-visible">
        {slantLayers.length > 0 ? (
          // Render multiple layers
          slantLayers.map((layer, index) => (
            <div
              key={`${layer.type}-${index}`}
              className={`flex flex-col justify-center items-start ${layer.type === 'availability' || layer.type === 'previousAvailability' ? 'items-center text-center' : ''}`}
              style={layer.styles}
            >
              {layer.content}
            </div>
          ))
        ) : (
          // Fallback to legacy single layer
          <div
            className="bg-gray-300 flex flex-col justify-center items-start"
            style={{
              ...getLegacySlantingStyles(daySlantStatus),
              backgroundColor: '#d1d5db',
            }}
          >
            <LockOutlined className="text-gray-600 text-sm mb-1" />
            {notes && (
              notes.length > 25 ? (
                <Tooltip
                  title={notes}
                  placement="top"
                  overlayClassName="max-w-xs"
                  mouseEnterDelay={0.3}
                >
                  <div className="text-xs text-gray-500 text-left w-full cursor-help">
                    {truncateText(notes, 25)}
                  </div>
                </Tooltip>
              ) : (
                <div className="text-xs text-gray-500 text-left w-full">
                  {notes}
                </div>
              )
            )}
          </div>
        )}
      </div>
    );
  }

  // Use enhanced analysis for all cases
  const daySlantStatus = getEnhancedSlantStatusForDate(date);

  // Generate multiple slant layers
  const slantLayers = generateSlantLayers(daySlantStatus);

  return (
    <div className="h-16 border-r border-gray-300 last:border-r-0 p-1 bg-white hover:bg-gray-50 transition-colors duration-150 overflow-visible">
      {(booking || daySlantStatus.hasCheckIn || daySlantStatus.hasCheckOut || daySlantStatus.hasBlockStart || daySlantStatus.hasBlockEnd) ? (
        <div className="relative h-full overflow-visible">
          {slantLayers.length > 0 ? (
            // Render multiple layers
            slantLayers.map((layer, index) => (
              <div
                key={`${layer.type}-${index}`}
                className={`flex flex-col justify-center ${layer.type === 'availability' || layer.type === 'previousAvailability' ? 'items-center text-center' : 'items-start'} ${getReservationColor()}`}
                style={layer.styles}
              >
                {layer.content}
              </div>
            ))
          ) : (
            // Fallback to legacy single layer for existing bookings
            <>
              {/* Previous date availability extension - NEW */}
              {daySlantStatus.hasPreviousAvailability && daySlantStatus.previousAvailabilityData && !daySlantStatus.hasCheckOut && !daySlantStatus.hasBlockEnd && cellIndex === 0 &&  (
                <div
                  className="bg-gray-100 flex flex-col justify-center items-center text-center opacity-75"
                  style={{
                    clipPath: 'polygon(0% 0%, 96% 0%, 80% 100%, 0% 100%)',
                    left: '-25%',
                    width: '68%',
                    zIndex: 0,
                    paddingLeft: '8%',
                    paddingRight: '8%',
                    position: 'absolute',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    height: '46px',
                  }}
                >
                  <div className="text-xs text-gray-700 font-medium">
                    AED {daySlantStatus.previousAvailabilityData.rate || 0}
                  </div>
                  <div className="text-xs text-gray-500">
                    {daySlantStatus.previousAvailabilityData.minNights || 0} nights min
                  </div>
                </div>
              )}

              {/* Availability layer */}
              {rate && (
                <div
                  className="bg-gray-100 flex flex-col justify-center items-center text-center"
                  style={{
                    clipPath: 'polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%)',
                    left: '37%',
                    width: '109%',
                    zIndex: 1,
                    paddingLeft: '12%',
                    paddingRight: '12%',
                    position: 'absolute',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    height: '46px',
                  }}
                >
                  <div className="text-xs text-gray-700 font-medium">
                    AED {rate.rate || 0}
                  </div>
                  <div className="text-xs text-gray-500">
                    {rate.minNights || 0} nights min
                  </div>
                </div>
              )}

              {/* Booking layer with legacy slanting */}
              <div
                className={`text-xs h-full flex flex-col justify-center ${getReservationColor()}`}
                style={{
                  ...getReservationStyle(),
                  ...getLegacySlantingStyles(daySlantStatus),
                  minHeight: '46px',
                }}
              >
                {showBookingText && (
                  <div className="absolute flex flex-col justify-center">
                    {(booking || daySlantStatus.checkInData) && (
                      <>
                        <div className="flex items-center gap-1">
                          {booking && getChannelIcon(booking.channel)}
                          <span className="truncate text-xs text-white font-medium">
                            {booking?.guestName ||
                             (isBooking(daySlantStatus.checkInData) ? daySlantStatus.checkInData.guestName : 'Guest')}
                          </span>
                        </div>
                        <div className="text-xs font-medium text-white">
                          AED {booking?.amount ||
                               (isBooking(daySlantStatus.checkInData) ? daySlantStatus.checkInData.amount : 0)}
                        </div>
                      </>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      ) : (
        <div className="h-full relative overflow-visible p-1">
          {/* Previous date availability extension for available dates - NEW */}
          { cellIndex === 0 && daySlantStatus.hasPreviousAvailability && daySlantStatus.previousAvailabilityData && !daySlantStatus.hasCheckOut && !daySlantStatus.hasBlockEnd&& (
            <div
              className="bg-gray-100 flex flex-col justify-center items-center text-center"
              style={{
                clipPath: 'polygon(0% 0%, 96% 0%, 80% 100%, 0% 100%)',
                left: '-25%',
                width: '68%',
                paddingLeft: '8%',
                paddingRight: '8%',
                border: 'none',
                borderRadius: '4px',
                margin: '0px',
                position: 'absolute',
                top: '50%',
                transform: 'translateY(-50%)',
                height: '46px',
                boxShadow: 'none',
                zIndex: 0,
              }}
            >
              <div className="text-xs text-gray-700 font-medium">
                AED {daySlantStatus.previousAvailabilityData.rate || 0}
              </div>
              <div className="text-xs text-gray-500">
                {daySlantStatus.previousAvailabilityData.minNights || 0}ghts min
              </div>
            </div>
          )}

          {/* Current date availability */}
          <div
            className="bg-gray-100 flex flex-col justify-center items-center text-center"
            style={{
              clipPath: 'polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%)',
              left: '37%',
              width: '109%',
              paddingLeft: '12%',
              paddingRight: '12%',
              border: 'none',
              borderRadius: '4px',
              margin: '0px',
              position: 'absolute',
              top: '50%',
              transform: 'translateY(-50%)',
              height: '46px',
              boxShadow: 'none',
              zIndex: 1,
            }}
          >
            <div className="text-xs text-gray-700 font-medium">
              AED {rate?.rate || 0}
            </div>
            <div className="text-xs text-gray-500">
              {rate?.minNights || 0} nights min
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CalendarCell;