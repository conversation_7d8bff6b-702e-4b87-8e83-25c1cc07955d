import Skeleton from "@mui/material/Skeleton";
import Box from "@mui/material/Box";

const PortfolioSkeleton: React.FC = () => {
  return (
    <>
        <div className="w-full inline-flex justify-end pt-8">
            <Box className="w-[150px] pr-2">
                <Skeleton className="px-6 py-3" />
            </Box>
            <Box className="w-[150px] text-left pr-4">
                <Skeleton className="px-6 py-3" />
            </Box>
            <Box className="w-[75px] text-left">
                <Skeleton className="px-6 py-3" />
            </Box>
        </div>
        <div>
          <Box className="flex w-full flex-row gap-2 grid grid-cols-7 gap-2">
              <Box>
                  <Skeleton className="p-8" />
              </Box>

              <Box>
                  <Skeleton className="p-8" />
              </Box>

              <Box>
                  <Skeleton className="p-8" />
              </Box>

              <Box>
                  <Skeleton className="p-8" />
              </Box>

              <Box>
                  <Skeleton className="p-8" />
              </Box>

              <Box>
                  <Skeleton className="p-8" />
              </Box>

              <Box>
                  <Skeleton className="p-8" />
              </Box>
            </Box>
        </div>
        <div className="w-full pt-0">
            <Box className="w-full">
                <Skeleton className="p-6" />
            </Box>
            <Box className="w-full">
                <Skeleton className="p-6" />
            </Box>
        </div>
    </>
  );
};

export default PortfolioSkeleton;
