import icon1 from "../../../src/assets/media/icon1.png";
import icon2 from "../../../src/assets/media/icon2.png";
import icon3 from "../../../src/assets/media/icon3.png";
import icon4 from "../../../src/assets/media/icon4.png";
import icon5 from "../../../src/assets/media/icon5.png";
import aboutUs from "../../../src/assets/media/about-us.png";


const features = [
  {
    icon: icon1,
    bgColor: "bg-teal-500",
    text: "Leverages data-driven strategies to maximize vacation rental revenue.",
  },
  {
    icon: icon2,
    bgColor: "bg-purple-600",
    text: "Aggregates PMS data to deliver accurate performance and revenue insights.",
  },
  {
    icon: icon3,
    bgColor: "bg-orange-400",
    text: "Provides detailed analytics on revenue, bookings, and owner commissions.",
  },
  {
    icon: icon4,
    bgColor: "bg-blue-500",
    text: "Tracks availability across all channels for full visibility and control.",
  },
  {
    icon: icon5,
    bgColor: "bg-green-500",
    text: "Enables channel-specific insights to assess revenue and performance by source.",
  },
];

const FeaturesSection = () => {
  return (
    <section className="text-white py-20 px-4" style={{ backgroundImage: `url(${aboutUs})` }}>
      <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8 text-center">
        {features.map((feature, index) => (
          <div key={index}>
            <div className={`mx-auto w-28 h-28 flex items-center justify-center rounded-full`}>
              <img src={feature.icon} alt={`Feature ${index + 1}`} className="w-30 h-30" />
            </div>
            <p className="mt-4 text-xl px-4 text-justify"  style={{ fontFamily: "sans-serif" }}>{feature.text}</p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default FeaturesSection;
