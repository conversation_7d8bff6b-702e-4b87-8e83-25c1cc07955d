import React, { useCallback, useEffect, useState } from "react";
import { DatePicker, Input, Skeleton } from "antd";
import dayjs from "dayjs";
import {
  getListingDetails,
  updateListingDetailsAPI,
} from "../../../../services/targetAPI";

export default function RatesAvailabilitySection({
  selectedListing,
}: {
  selectedListing: string;
}) {
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [listingDetails, setListingDetails] = useState<any | null>(null);
  const [editedDetails, setEditedDetails] = useState<any | null>(null);
  const [dateError, setDateError] = useState<string | null>(null);

  const loadListingDetails = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getListingDetails(selectedListing, "pricing");
      setListingDetails(res);
    } catch (error) {
      console.error("Failed to fetch listings:", error);
    } finally {
      setLoading(false);
    }
  }, [selectedListing]);

  useEffect(() => {
    selectedListing && loadListingDetails();
  }, [selectedListing]);

  useEffect(() => {
    if (listingDetails) {
      setEditedDetails({
        contract: { ...listingDetails.contract },
        managementFees: listingDetails.managementFees,
        dtcmExpiry: listingDetails.dtcmExpiry,
      });
    }
  }, [listingDetails]);

  const handleChange = (field: string, value: string) => {
    setEditedDetails((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleContractChange = (field: string, value: dayjs.Dayjs | null) => {
    const newDate = value ? value.format("YYYY-MM-DD") : null;
    const updated = {
      ...editedDetails.contract,
      [field]: newDate,
    };

    if (
      updated.startDate &&
      updated.endDate &&
      dayjs(updated.startDate).isAfter(dayjs(updated.endDate))
    ) {
      setDateError("Start date cannot be after end date.");
    } else {
      setDateError(null);
    }

    setEditedDetails((prev: any) => ({
      ...prev,
      contract: updated,
    }));
  };

  const handleSave = async () => {
    try {
      await updateListingDetailsAPI(selectedListing, {
        start_date: editedDetails.contract.startDate,
        end_date: editedDetails.contract.endDate,
        pm_fees: editedDetails.managementFees,
        dtcm_expiry_date: editedDetails.dtcmExpiry,
      });
      await loadListingDetails();
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to save changes:", error);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedDetails({
      contract: { ...listingDetails.contract },
      managementFees: listingDetails.managementFees,
      dtcmExpiry: listingDetails.dtcmExpiry,
    });
  };

  if (loading || !listingDetails || !editedDetails) {
    return (
      <div>
        <div className="space-y-6">
          <Skeleton active paragraph={{ rows: 3 }} />
          <Skeleton active paragraph={{ rows: 2 }} />
        </div>
      </div>
    );
  }

  const { rates } = listingDetails;
  const { contract, managementFees, dtcmExpiry } = editedDetails;

  return (
    <div className="space-y-10 text-gray-800 text-sm">
      {/* Rates Section */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Rates</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <div className="text-gray-500 font-medium">Base Rate</div>
            <div>{rates.baseRate}</div>
          </div>
          <div>
            <div className="text-gray-500 font-medium">Cleaning Fees</div>
            <div>{rates.cleaningFees}</div>
          </div>
          <div>
            <div className="text-gray-500 font-medium">VAT</div>
            <div>{rates.vat}</div>
          </div>
          <div>
            <div className="text-gray-500 font-medium">City / Tourism Tax</div>
            <div>{rates.cityTax}</div>
          </div>
        </div>
      </section>

      {/* Contract Section */}
      <section>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Contract</h2>
          {!isEditing && (
            <button
              className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors"
              onClick={() => setIsEditing(true)}
            >
              Edit
            </button>
          )}
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-gray-500 font-medium mb-1">
              Start Date
            </label>
            {isEditing ? (
              <DatePicker
                value={contract.startDate ? dayjs(contract.startDate) : null}
                onChange={(date) => handleContractChange("startDate", date)}
                className="w-full max-w-sm"
                format="YYYY-MM-DD"
                allowClear
                status={dateError ? "error" : ""}
              />
            ) : (
              <div>{contract.startDate || "N/A"}</div>
            )}
          </div>
          <div>
            <label className="block text-gray-500 font-medium mb-1">
              End Date
            </label>
            {isEditing ? (
              <DatePicker
                value={contract.endDate ? dayjs(contract.endDate) : null}
                onChange={(date) => handleContractChange("endDate", date)}
                className="w-full max-w-sm"
                format="YYYY-MM-DD"
                allowClear
                status={dateError ? "error" : ""}
              />
            ) : (
              <div>{contract.endDate || "N/A"}</div>
            )}
          </div>
          {dateError && (
            <div className="text-red-500 text-sm col-span-2">{dateError}</div>
          )}
        </div>
      </section>

      {/* Management Fees Section */}
      <section>
        <h2 className="text-lg font-semibold mb-3">Management Fees</h2>
        {isEditing ? (
          <Input
            value={managementFees}
            onChange={(e) => handleChange("managementFees", e.target.value)}
            className="w-full max-w-sm"
          />
        ) : (
          <div>{listingDetails.managementFees}</div>
        )}
      </section>

      {/* DTCM Expiry Section */}
      <section>
        <h2 className="text-lg font-semibold mb-3">DTCM Expiry</h2>
        {isEditing ? (
          <DatePicker
            value={dtcmExpiry ? dayjs(dtcmExpiry) : null}
            onChange={(date) =>
              handleChange("dtcmExpiry", date?.format("YYYY-MM-DD") || "")
            }
            className="w-full max-w-sm"
            format="YYYY-MM-DD"
            allowClear
          />
        ) : (
          <div>{listingDetails.dtcmExpiry || "N/A"}</div>
        )}
      </section>

      {/* Save/Cancel Buttons */}
      {isEditing && (
        <div className="flex gap-4 pt-2">
          <button
            onClick={handleSave}
            disabled={!!dateError}
            className={`text-sm px-4 py-1 rounded transition-colors ${
              dateError
                ? "bg-gray-300 text-gray-600 cursor-not-allowed"
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            Save
          </button>
          <button
            onClick={handleCancel}
            className="px-4 py-1 border rounded text-gray-700"
          >
            Cancel
          </button>
        </div>
      )}
    </div>
  );
}
