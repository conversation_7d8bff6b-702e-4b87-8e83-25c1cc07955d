export interface RevenueType {
  month: string;
  target_amount: string;
  currency: string;
  comparison?: string;
}

export type MainTableChanges = {
  [id: string]: {
    [field: string]: string | number;
  };
};

export type PMTableChanges = {
  [id: string]: {
    [month: string]: string | number;
  };
};

export interface Property {
  targets: any;
  pmFees: any;
  id: number | string | any;
  internal_listing_name: string;
  start_date: string;
  end_date: string;
  pm_fees: number | string;
  is_pm_fees_from_net: boolean;
  listingid: number | string;
  pms_name: number | string;
}

export interface MainTableItem {
  id: number;
  internal_listing_name?: string;
  to_do_flag?: string;
  address?: string;
  bedroom?: number;
  pm_fees?: number | string | null;
  start_date?: string | null;
  end_date?: string | null;
  is_pm_fees_from_net?: boolean | null;
}

export interface ResponseData {
  month: string;
  target_amount: string;
}

export interface PropertyChanges {
  [monthYear: string]: number;
}

export interface TargetTableChanges {
  [propertyID: string]: PropertyChanges;
}
export interface Property_Listing {
  is_pm_fees_from_net: boolean;
  start_date: string;
  end_date: string;
  pm_fees: string;
  id: number;
  internal_listing_name: string;
  currency?: string;
  revenues: RevenueType[];
  name?: string;
  listingid: number | string;
  pms_name: number | string;
}

export interface TargetTableProps {
  activeSelection: string;
  searchValue: string;
}

export interface MonthlyData {
  id: number;
  [key: string]: string | number;
}
