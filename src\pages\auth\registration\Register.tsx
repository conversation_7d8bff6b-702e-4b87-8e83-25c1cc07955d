import axios from "axios";
import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import { Link } from "react-router-dom";
export const Register: React.FC = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");

    const handleSubmit = async (e: React.FormEvent) => {
            e.preventDefault();
            if (password !== confirmPassword) {
              alert("Passwords do not match");
              return;
            }
            try {
              await axios.post("/api/signup", { email, password });
              // Handle successful sign up
            } catch (error) {
              console.log(error);
            }
        };

return (
    <div className="flex items-center justify-center bg-gray-100">
      <form
        onSubmit={handleSubmit}
        className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md space-y-6"
      >
        <h1 className="text-2xl font-bold text-center mb-2">Register</h1>

        <TextField
          label="Email"
          variant="outlined"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          fullWidth
          className="mb-4"
        />

        <TextField
          label="Password"
          variant="outlined"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          fullWidth
          className="mb-4"
        />

        <TextField
          label="Confirm Password"
          variant="outlined"
          type="password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          fullWidth
          className="mb-4"
        />

        {/* <Button
          type="submit"
          variant="contained"
          color="primary"
          fullWidth
          className="bg-blue-500 hover:bg-blue-600"
        >
          Sign Up
        </Button>

        <div className="text-center mt-4">
          <p className="text-sm text-gray-600">
            Already have an account?{" "}
            <Link to="/login" className="text-blue-500 hover:underline">
              Login
            </Link>
          </p>
        </div> */}
      </form>
    </div>
  );
}

export default Register;