import { Avatar, Typography } from "@mui/material";
import { getRandomColor } from "../utils";

const ChatHeader = ({ name }: { name?: string }) => {
  return (
    <div className="flex items-center h-16 flex justify-between px-6 py-2 border-b">
      <div className="items-center">
        <Typography variant="h5" fontWeight="bold">
          {name || "Guest"}
        </Typography>
      </div>
    </div>
  );
};

export default ChatHeader;
