
const BookingDetailsCard = ({ reservationDetails }: any) => {
  return (
    <div className="bg-white shadow-sm rounded-lg px-4 py-2 space-y-2">
      <h3 className="text-sm font-semibold text-gray-700 mb-2">
        BOOKING DETAILS
      </h3>
      <div className="grid grid-cols-2 gap-4 text-sm text-gray-700">
        <div>
          <p className="text-gray-500">Reservation date</p>
          <p>{reservationDetails?.reservation_date || "—"}</p>
        </div>
        <div>
          <p className="text-gray-500">Reservation ID</p>
          <p>{reservationDetails?.pms_table_reservationid || "—"}</p>
        </div>
        <div>
          <p className="text-gray-500">Checkin date</p>
          <p>{reservationDetails?.checkIn || "—"}</p>
        </div>
        <div>
          <p className="text-gray-500">Checkout date</p>
          <p>{reservationDetails?.checkOut || "—"}</p>
        </div>
        <div>
          <p className="text-gray-500">Nights</p>
          <p>{reservationDetails?.nights ?? "—"}</p>
        </div>
        <div>
          <p className="text-gray-500">Guests</p>
          <p>{reservationDetails?.guest_count ?? "—"}</p>
        </div>
        <div>
          <p className="text-gray-500">Price</p>
          <p>{reservationDetails?.currency ?? ""} {reservationDetails?.total_price?.toLocaleString() || "—"}</p>
        </div>
        <div>
          <p className="text-gray-500">Paid</p>
          <p>{reservationDetails?.currency ?? ""} {reservationDetails?.paid?.toLocaleString() || "—"}</p> {/* Update if you have payment info */}
        </div>
      </div>
    </div>
  );
};

export default BookingDetailsCard;
