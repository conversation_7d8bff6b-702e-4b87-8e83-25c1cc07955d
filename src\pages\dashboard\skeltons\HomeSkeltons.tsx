import Skeleton from "@mui/material/Skeleton";
import Box from "@mui/material/Box";

const HomeSkeleton: React.FC = () => {
  return (
    <Box className="grid w-full grid-cols-3 gap-4">
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
      <Skeleton className="min-h-80 h-72 rounded-md" variant="rectangular" />
    </Box>
  );
};

export default HomeSkeleton;

